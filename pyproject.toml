[project]
name = "t-ai2"
version = "0.1.0"
description = "t-ai2"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    # internal
    "t-ai-web",
    "t-ai-agent",
    #external
    "loguru>=0.7.3",
    "openinference-instrumentation-openai-agents>=1.0.0"
]


[dependency-groups]
dev = [
    "tach>=0.29.0",
    "pytest-asyncio>=0.26.0",
    "pytest>=8.3.5",
    "memray>=1.17.2",
    "pre-commit>=4.2.0",
]




[tool.uv.sources]
t-ai-web = { workspace = true }
t-ai-common = { workspace = true }
t-ai-agent = { workspace = true }
t-ai-app = { workspace = true }
t-ai-chat = { workspace = true }
t-ai-knowledge-base = { workspace = true }
t-ai-docx = { workspace = true }
t-ai-mcp = { workspace = true }
t-ai-agent-ops = { workspace = true }



[tool.uv.workspace]
members = [
    "packages/t-ai-agent",
    "packages/t-ai-app",
    "packages/t-ai-common",
    "packages/t-ai-web",
    "packages/t-ai-chat",
    "packages/t-ai-knowledge-base",
    "packages/t-ai-docx",
    "packages/t-ai-mcp",
    "packages/t-ai-agent-ops"
]


[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["src/t_ai2"]

# 设置 index 源为国内源
# note: 如果用uv sync --frozen 会使用当前的lock文件 而会忽略这个配置
# 所以如果你的lock已经生成而用的不是预期的源 那么需要删除lock 重新配置源后再生成lock
# 这个时候 uv sync --frozen 就没问题了
[[tool.uv.index]]
url = "https://pypi.tuna.tsinghua.edu.cn/simple/"
default = true

# pytest 设置
[tool.pytest.ini_options]
asyncio_mode = "auto"
log_cli = "False"
log_cli_level = "INFO"
log_cli_format = "%(asctime)s [%(levelname)s] | %(filename)s:%(lineno)s | %(message)s"
log_cli_date_format = "%Y-%m-%d %H:%M:%S"
