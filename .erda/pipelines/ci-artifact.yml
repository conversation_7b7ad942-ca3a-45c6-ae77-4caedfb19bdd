version: "1.1"
name: ""
"on":
  push:
    branches:
      - release/develop
stages:
  - stage:
      - git-checkout:
          alias: repo
          description: 代码仓库克隆
          params:
            depth: 1
  - stage:
      - custom-script:
          alias: ci-version
          description: 运行自定义命令
          commands:
            - OPERATOR_NAME=$([ "((pipeline.trigger.mode))" == "cron"  ]  && echo "定时任务" || echo "((dice.operator.name))")
            - echo "operator-name=$OPERATOR_NAME" >> $METAFILE
            - echo ${repo}
            - cd ${repo}
            - sh ${repo}/scripts/ci-artifact-version.sh
  - stage:
      - dockerfile:
          alias: build-app
          description: 针对自定义 dockerfile 打包，产出可运行镜像
          params:
            image:
              name: t-ai2
              tag: v1.0.3
            path: ./Dockerfile
            registry:
              password: ${{ configs.docker_registry_password }}
              url: registry.erda.cloud/trantor
              username: ${{ configs.docker_registry_username }}
            workdir: ${repo}
  - stage:
      - release:
          alias: release
          description: 用于打包完成时，向dicehub 提交完整可部署的dice.yml。用户若没在pipeline.yml里定义该action，CI会自动在pipeline.yml里插入该action
          params:
            dice_yml: ${repo}/dice.yml
            image:
              t-ai2: ${build-app:OUTPUT:image}
            tag_version: ${{ outputs.ci-version.artifact-version }}