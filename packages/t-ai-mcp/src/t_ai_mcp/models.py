from datetime import timed<PERSON><PERSON>
from typing import Any, Callable, Literal, <PERSON><PERSON><PERSON><PERSON>

from mcp.types import Tool as MCPTool
from pydantic import BaseModel, Field

REQUEST_ID_FACTORY: TypeAlias = Callable[[], str]

class TMCPServerConfig(BaseModel):
    """
    TMCP Server Config
    """
    server_endpoint: str = Field(description="mcp server endpoint")
    transport_mode: Literal["sse", "streamable_http"] = Field(description="transport mode", default="sse")
    server_args: dict[str, Any] | None = Field(description="mcp server args", default=None)
    headers_factory: Callable[[], dict[str, str]] | None = Field(
        description="factory to generate client headers dynamically", default=None
    )
    timeout: timedelta = Field(description="client timeout", default=timedelta(seconds=15))
    sse_read_timeout: timedelta = Field(description="sse read timeout(s)", default=timedelta(seconds=30))
    client_session_timeout: timedelta | None = Field(description="session timeout", default=None)
    terminate_on_close: bool = Field(description="terminate on close", default=True)
    request_id_factory: REQUEST_ID_FACTORY | None = Field(
        description="request id factory can generate request id", default=None
    )
    use_structured_content: bool = Field(
        description="Whether to use structured_content when calling MCP tools",
        default=False,
    )
    list_tools: list[MCPTool] | None = Field(
        description="list tools", default=None
    )
    lazy: bool = Field(
        description="Whether to lazy load the tools, if true, the tools will be loaded when the first request is made",
        default=False,
    )
    # other hooks


class TMCPTransportArgs(BaseModel):
    """
    TMP transport args.
    This is a simple wrapper of args during mcp transport.
    we can use this to pass custom args to mcp server.
    note: mcp servers may check the args e.g. fastmcp will check the args by using pydantic
    """

    request_id: str | None = Field(
        description="request id",
        serialization_alias="__request_id__",
        default=None,
    )
    args: dict[str, Any] | None = Field(description="args transport", default={})
    extra: dict[str, Any] | None = Field(description="extra args", default=None, serialization_alias="__extra__")

    def to_dict(self) -> dict[str, Any]:
        """to dict make a big dict contains args and other keys in model"""
        _args = self.model_dump(by_alias=True, exclude_none=True)
        new_args = {k: v for k, v in _args.items() if k != "args"}
        new_args.update(self.args or {})
        return new_args
