from typing import Optional

from pydantic import Field
from pydantic_settings import BaseSettings


class MilvusSettings(BaseSettings):
    """Milvus 向量数据库配置"""

    host: str = Field(default="************", alias="MILVUS_HOST", description="Milvus服务器地址")

    port: int = Field(default=19530, alias="MILVUS_PORT", description="Milvus服务器端口")

    user: Optional[str] = Field(default="root", alias="MILVUS_USER", description="Milvus用户名")

    password: Optional[str] = Field(default="Milvus", alias="MILVUS_PASSWORD", description="Milvus密码")

    db_name: str = Field(default="knowledge_base", alias="MILVUS_DB_NAME", description="Milvus数据库名称")

    collection_name: str = Field(
        default="knowledge_base_documents", alias="MILVUS_COLLECTION_NAME", description="知识库文档集合名称"
    )

    dimension: int = Field(default=1536, alias="MILVUS_DIMENSION", description="向量维度")

    @property
    def uri(self) -> str:
        """构建Milvus连接URI"""
        # 如果用户名和密码都存在且不为空，则使用认证
        if self.user and self.password and self.user.strip() and self.password.strip():
            return f"http://{self.user}:{self.password}@{self.host}:{self.port}"
        # 否则不使用认证
        return f"http://{self.host}:{self.port}"


class EmbeddingSettings(BaseSettings):
    """向量化配置"""

    model_name: str = Field(default="text-embedding-ada-002", alias="EMBEDDING_MODEL_NAME", description="嵌入模型名称")

    chunk_size: int = Field(default=800, alias="DEFAULT_CHUNK_SIZE", description="默认分块大小")

    chunk_overlap: int = Field(default=200, alias="DEFAULT_CHUNK_OVERLAP", description="默认分块重叠")


class KnowledgeBaseConfig(BaseSettings):
    """知识库全局配置"""

    milvus: MilvusSettings = MilvusSettings()
    embedding: EmbeddingSettings = EmbeddingSettings()

    class Config:
        env_file = ".env"  # 支持从 .env 文件读取配置
        env_file_encoding = "utf-8"
        case_sensitive = False


# 全局配置实例
kb_config = KnowledgeBaseConfig()
