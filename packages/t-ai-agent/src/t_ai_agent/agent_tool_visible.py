from t_ai_agent.model.agent_dsl import ToolVisible


def get_tool_call_visible(tool_visible: ToolVisible):
    visible = True
    if tool_visible is not None:
        if tool_visible.enabled is False:
            visible = False
    return visible


def get_tool_call_arguments_visible(tool_visible: ToolVisible):
    visible = True
    if tool_visible is not None:
        if tool_visible.enabled is False:
            visible = False
        elif tool_visible.tool_arguments is False:
            visible = False
    return visible


def get_tool_call_output_visible(tool_visible: ToolVisible):
    visible = True
    if tool_visible is not None:
        if tool_visible.enabled is False:
            visible = False
        elif tool_visible.tool_output is False:
            visible = False
    return visible
