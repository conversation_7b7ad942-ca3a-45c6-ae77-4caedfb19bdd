import ast
from typing import Any, List
from loguru import logger

from .py_sandbox import PythonSandbox
from .model import RunSandboxRequest, ValidateCodeRequest, ValidationResult


async def run_sandbox(request: RunSandboxRequest) -> Any:
    """
    执行Python代码沙箱
    """
    code = request.code
    arguments = request.arguments

    # 创建沙箱实例
    sandbox = PythonSandbox()

    # 执行Python代码，传递参数
    execution_result = sandbox.run_code(code, arguments)

    # 返回执行结果
    return execution_result.to_dict()


async def validate_python_code(request: ValidateCodeRequest) -> ValidationResult:
    """
    校验Python代码的语法、安全性和合规性

    直接复用 PythonSandbox 的 _is_safe_code 方法进行校验

    Args:
        request: 校验请求，包含代码和校验选项

    Returns:
        ValidationResult: 校验结果
    """
    logger.info(f"开始校验Python代码，代码长度: {len(request.code)} 字符")

    try:
        # 创建沙箱实例，使用指定的允许模块列表
        sandbox = PythonSandbox(allowed_modules=request.allowed_modules)

        # 直接使用沙箱的安全检查方法，它已经包含了语法检查、安全检查和导入检查
        is_safe, error_message = sandbox._is_safe_code(request.code)

        # 收集代码详细信息
        details = _collect_code_details(request.code)

        if is_safe:
            logger.info("Python代码校验通过")
            return ValidationResult(
                is_valid=True,
                syntax_valid=True,
                security_valid=True,
                imports_valid=True,
                details=details
            )
        else:
            logger.warning(f"Python代码校验失败: {error_message}")
            return ValidationResult(
                is_valid=False,
                syntax_valid="语法错误" not in error_message,
                security_valid="不允许" not in error_message,
                imports_valid="不允许导入" not in error_message and "不允许从模块" not in error_message,
                errors=[error_message],
                details=details
            )

    except Exception as e:
        logger.error(f"代码校验过程中发生异常: {e}")
        return ValidationResult(
            is_valid=False,
            syntax_valid=False,
            security_valid=False,
            imports_valid=False,
            errors=[f"校验过程异常: {str(e)}"]
        )


def _collect_code_details(code: str) -> dict:
    """
    收集代码的详细信息

    Args:
        code: Python代码字符串

    Returns:
        dict: 代码详细信息
    """
    details = {
        "line_count": len(code.split('\n')),
        "char_count": len(code),
        "functions": [],
        "classes": [],
        "imports": [],
        "has_main": False
    }

    try:
        tree = ast.parse(code)

        for node in ast.walk(tree):
            # 收集函数定义
            if isinstance(node, ast.FunctionDef):
                details["functions"].append(node.name)
                if node.name == "main":
                    details["has_main"] = True

            # 收集类定义
            elif isinstance(node, ast.ClassDef):
                details["classes"].append(node.name)

            # 收集导入信息
            elif isinstance(node, ast.Import):
                for alias in node.names:
                    details["imports"].append(alias.name)

            elif isinstance(node, ast.ImportFrom):
                module = node.module or ""
                for alias in node.names:
                    import_info = f"{module}.{alias.name}" if module else alias.name
                    details["imports"].append(import_info)

    except Exception as e:
        details["parse_error"] = str(e)

    return details
