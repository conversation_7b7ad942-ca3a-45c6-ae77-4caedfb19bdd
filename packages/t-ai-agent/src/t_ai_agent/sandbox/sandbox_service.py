import ast
from typing import Any, List, Tuple
from loguru import logger

from .py_sandbox import PythonSandbox
from .model import RunSandboxRequest, ValidateCodeRequest, ValidationResult


async def run_sandbox(request: RunSandboxRequest) -> Any:
    """
    执行Python代码沙箱
    """
    code = request.code
    arguments = request.arguments

    # 创建沙箱实例
    sandbox = PythonSandbox()

    # 执行Python代码，传递参数
    execution_result = sandbox.run_code(code, arguments)

    # 返回执行结果
    return execution_result.to_dict()


async def validate_python_code(request: ValidateCodeRequest) -> ValidationResult:
    """
    校验Python代码的语法、安全性和合规性

    Args:
        request: 校验请求，包含代码和校验选项

    Returns:
        ValidationResult: 校验结果
    """
    logger.info(f"开始校验Python代码，代码长度: {len(request.code)} 字符")

    result = ValidationResult(
        is_valid=True,
        syntax_valid=True,
        security_valid=True,
        imports_valid=True
    )

    try:
        # 1. 语法检查
        if request.check_syntax:
            syntax_valid, syntax_error = _check_syntax(request.code)
            result.syntax_valid = syntax_valid
            if not syntax_valid:
                result.errors.append(f"语法错误: {syntax_error}")
                result.is_valid = False
                logger.warning(f"语法检查失败: {syntax_error}")

        # 2. 安全性检查
        if request.check_security and result.syntax_valid:
            security_valid, security_error = _check_security(request.code, request.allowed_modules)
            result.security_valid = security_valid
            if not security_valid:
                result.errors.append(f"安全检查失败: {security_error}")
                result.is_valid = False
                logger.warning(f"安全检查失败: {security_error}")

        # 3. 导入模块检查
        if request.check_imports and result.syntax_valid:
            imports_valid, import_errors, import_warnings = _check_imports(request.code, request.allowed_modules)
            result.imports_valid = imports_valid
            if not imports_valid:
                result.errors.extend(import_errors)
                result.is_valid = False
            result.warnings.extend(import_warnings)
            if import_errors:
                logger.warning(f"导入检查失败: {import_errors}")

        # 4. 收集详细信息
        result.details = _collect_code_details(request.code)

        if result.is_valid:
            logger.info("Python代码校验通过")
        else:
            logger.warning(f"Python代码校验失败，错误: {result.errors}")

    except Exception as e:
        logger.error(f"代码校验过程中发生异常: {e}")
        result.is_valid = False
        result.syntax_valid = False
        result.security_valid = False
        result.imports_valid = False
        result.errors.append(f"校验过程异常: {str(e)}")

    return result


def _check_syntax(code: str) -> Tuple[bool, str]:
    """
    检查Python代码语法

    Args:
        code: Python代码字符串

    Returns:
        Tuple[bool, str]: (是否语法正确, 错误信息)
    """
    try:
        ast.parse(code)
        return True, ""
    except SyntaxError as e:
        error_msg = f"第{e.lineno}行: {e.msg}"
        if e.text:
            error_msg += f" (代码: {e.text.strip()})"
        return False, error_msg
    except Exception as e:
        return False, f"语法分析异常: {str(e)}"


def _check_security(code: str, allowed_modules: List[str] = None) -> Tuple[bool, str]:
    """
    检查代码安全性

    Args:
        code: Python代码字符串
        allowed_modules: 允许的模块列表

    Returns:
        Tuple[bool, str]: (是否安全, 错误信息)
    """
    try:
        # 创建沙箱实例进行安全检查
        sandbox = PythonSandbox(allowed_modules=allowed_modules)
        is_safe, error_msg = sandbox._is_safe_code(code)
        return is_safe, error_msg
    except Exception as e:
        return False, f"安全检查异常: {str(e)}"


def _check_imports(code: str, allowed_modules: List[str] = None) -> Tuple[bool, List[str], List[str]]:
    """
    检查导入模块的合规性

    Args:
        code: Python代码字符串
        allowed_modules: 允许的模块列表

    Returns:
        Tuple[bool, List[str], List[str]]: (是否合规, 错误列表, 警告列表)
    """
    errors = []
    warnings = []

    try:
        tree = ast.parse(code)

        # 默认允许的模块
        default_allowed = [
            'json', 'datetime', 'time', 'math', 'random', 'uuid', 'base64',
            'hashlib', 'hmac', 'urllib', 'requests', 're', 'collections',
            'itertools', 'functools', 'operator', 'typing', 'dataclasses',
            'decimal', 'fractions', 'statistics', 'string', 'textwrap'
        ]

        allowed = allowed_modules if allowed_modules is not None else default_allowed

        for node in ast.walk(tree):
            if isinstance(node, (ast.Import, ast.ImportFrom)):
                # 处理 from module import ... 语句
                if isinstance(node, ast.ImportFrom):
                    module = node.module or ""
                    root_module = module.split('.')[0] if module else ""

                    # 检查是否为内置模块或允许的模块
                    if root_module and root_module not in allowed:
                        # 特殊处理：允许从 t_ai_agent 导入
                        if root_module == "t_ai_agent":
                            warnings.append(f"从内部模块导入: {module}")
                        else:
                            errors.append(f"不允许从模块 '{root_module}' 导入")

                # 处理 import module 语句
                for alias in node.names:
                    module_name = alias.name.split('.')[0]

                    if module_name not in allowed:
                        # 特殊处理：允许导入 t_ai_agent
                        if module_name == "t_ai_agent":
                            warnings.append(f"导入内部模块: {alias.name}")
                        else:
                            errors.append(f"不允许导入模块 '{module_name}'")

        is_valid = len(errors) == 0
        return is_valid, errors, warnings

    except Exception as e:
        errors.append(f"导入检查异常: {str(e)}")
        return False, errors, warnings


def _collect_code_details(code: str) -> dict:
    """
    收集代码的详细信息

    Args:
        code: Python代码字符串

    Returns:
        dict: 代码详细信息
    """
    details = {
        "line_count": len(code.split('\n')),
        "char_count": len(code),
        "functions": [],
        "classes": [],
        "imports": [],
        "has_main": False
    }

    try:
        tree = ast.parse(code)

        for node in ast.walk(tree):
            # 收集函数定义
            if isinstance(node, ast.FunctionDef):
                details["functions"].append(node.name)
                if node.name == "main":
                    details["has_main"] = True

            # 收集类定义
            elif isinstance(node, ast.ClassDef):
                details["classes"].append(node.name)

            # 收集导入信息
            elif isinstance(node, ast.Import):
                for alias in node.names:
                    details["imports"].append(alias.name)

            elif isinstance(node, ast.ImportFrom):
                module = node.module or ""
                for alias in node.names:
                    import_info = f"{module}.{alias.name}" if module else alias.name
                    details["imports"].append(import_info)

    except Exception as e:
        details["parse_error"] = str(e)

    return details
