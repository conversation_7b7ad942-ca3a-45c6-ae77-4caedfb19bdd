from typing import Optional, Any, List
from pydantic import BaseModel, Field


class RunSandboxRequest(BaseModel):
    """
    Request model for sandbox code execution.
    """
    code: str
    arguments: Optional[Any] = None


class ValidateCodeRequest(BaseModel):
    """
    Request model for Python code validation.
    """
    code: str = Field(..., description="要校验的Python代码字符串")
    allowed_modules: Optional[List[str]] = Field(
        default=None,
        description="允许导入的模块列表，如果为None则使用默认列表"
    )
    check_syntax: bool = Field(default=True, description="是否检查语法错误")
    check_security: bool = Field(default=True, description="是否检查安全性")
    check_imports: bool = Field(default=True, description="是否检查导入模块")


class ValidationResult(BaseModel):
    """
    Code validation result.
    """
    is_valid: bool = Field(..., description="代码是否有效")
    errors: List[str] = Field(default_factory=list, description="错误信息列表")
    warnings: List[str] = Field(default_factory=list, description="警告信息列表")
    syntax_valid: bool = Field(..., description="语法是否正确")
    security_valid: bool = Field(..., description="安全性是否通过")
    imports_valid: bool = Field(..., description="导入模块是否合规")
    details: dict = Field(default_factory=dict, description="详细检查信息")
