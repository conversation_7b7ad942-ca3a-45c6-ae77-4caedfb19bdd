"""
Python代码动态执行沙箱模块。
该模块提供在受控环境中安全执行Python代码的功能。
"""

import sys
import io
import os
import tempfile
import uuid
import traceback
import time
import resource
from typing import Dict, Any, Tuple, Optional, List, Union
import ast
import contextlib
import json


class CodeExecutionResult:
    """
    用于保存代码执行结果的类，用于API响应。
    """

    def __init__(
        self,
        success: bool,
        result: Any = None,
        output: str = "",
        error: str = "",
        execution_time: float = 0.0,
        temp_file: str = None,
    ):
        """
        初始化执行结果。

        Args:
            success: 执行是否成功
            result: 执行的返回值（如果有的话）
            output: 标准输出/错误输出
            error: 执行失败时的错误信息
            execution_time: 代码执行耗时（秒）
            temp_file: 如果代码保存到磁盘，则为临时文件路径
        """
        self.success = success
        self.result = result
        self.output = output
        self.error = error
        self.execution_time = execution_time
        self.temp_file = temp_file
    
    def to_dict(self) -> Dict[str, Any]:
        """将执行结果转换为字典，用于API响应。"""
        return {
            "success": self.success,
            "result": self.result,
            "output": self.output,
            "error": self.error,
            "execution_time": self.execution_time,
            "temp_file": self.temp_file,
        }

    def to_json(self) -> str:
        """将执行结果转换为JSON字符串，用于API响应。"""
        # 使用自定义序列化器处理不可序列化的对象
        return json.dumps(
            self.to_dict(),
            default=lambda o: str(o) if not isinstance(o, (dict, list, str, int, float, bool, type(None))) else o,
            ensure_ascii=False,
        )


class PythonSandbox:
    """
    用于动态执行Python代码的沙箱环境，具有受控的资源访问权限。
    """

    # 安全执行的默认允许模块列表
    DEFAULT_ALLOWED_MODULES = [
        "math", "random", "time", "datetime", "collections", "itertools", "functools", "re", "json", "heapq",
        "t_ai_agent", "traceback"
    ]

    # 代码执行的默认时间限制（秒）
    DEFAULT_TIME_LIMIT = 5.0

    # 代码执行的默认内存限制（字节，100MB）
    DEFAULT_MEMORY_LIMIT = 100 * 1024 * 1024
    
    def __init__(
        self,
        allowed_modules: Optional[List[str]] = None,
        time_limit: float = DEFAULT_TIME_LIMIT,
        memory_limit: int = DEFAULT_MEMORY_LIMIT,
        temp_dir: Optional[str] = None
    ):
        """
        使用可选限制初始化沙箱。

        Args:
            allowed_modules: 允许导入的模块名称列表。
                            如果为None，则使用默认允许模块列表。
            time_limit: 最大执行时间（秒）
            memory_limit: 最大内存使用量（字节）
            temp_dir: 存储临时文件的目录。如果为None，则使用系统临时目录。
        """
        self.allowed_modules = allowed_modules or self.DEFAULT_ALLOWED_MODULES
        self.time_limit = time_limit
        self.memory_limit = memory_limit
        self.temp_dir = temp_dir or tempfile.gettempdir()
        self.locals_dict: Dict[str, Any] = {}
        self.globals_dict: Dict[str, Any] = {}

        # 如果沙箱目录不存在则创建
        self.sandbox_dir = os.path.join(self.temp_dir, "python_sandbox")
        os.makedirs(self.sandbox_dir, exist_ok=True)

        # 使用安全的内置函数初始化全局字典
        self._setup_environment()
    
    def _setup_environment(self):
        """设置执行环境，包含必要的内置函数和限制。"""
        # 从受限的内置函数开始创建新的全局字典
        import builtins

        # 创建安全的内置函数子集
        safe_builtins = {}
        for name in dir(builtins):
            if name not in [
                'open', 'eval', 'exec', 'compile',
                'input', 'memoryview', 'breakpoint', 'globals'
                # 注意：允许 __import__ 函数以支持模块导入
            ]:
                safe_builtins[name] = getattr(builtins, name)

        self.globals_dict = {
            '__builtins__': safe_builtins,
        }

        # 将允许的模块添加到全局字典
        for module_name in self.allowed_modules:
            try:
                if module_name == "t_ai_agent":
                    # 对项目内部模块的特殊处理
                    import t_ai_agent
                    self.globals_dict[module_name] = t_ai_agent
                else:
                    # 导入普通模块
                    module = __import__(module_name)
                    self.globals_dict[module_name] = module
            except ImportError:
                # 跳过无法导入的模块
                pass

        # 初始化空的局部变量字典
        self.locals_dict = {}
    
    def _is_safe_code(self, code_str: str) -> Tuple[bool, str]:
        """
        分析代码以检查潜在的不安全操作。

        Args:
            code_str: 要分析的代码字符串

        Returns:
            包含以下内容的元组：
                - 布尔值，指示代码是否安全
                - 如果代码不安全则返回错误信息，否则返回空字符串
        """
        try:
            # 将代码解析为AST
            tree = ast.parse(code_str)

            print(f"安全检查: 开始分析代码安全性")

            # 检查潜在的危险操作
            for node in ast.walk(tree):
                # 检查允许列表之外的导入
                if isinstance(node, (ast.Import, ast.ImportFrom)):
                    # 处理 ImportFrom 语句
                    if isinstance(node, ast.ImportFrom):
                        module = node.module or ""
                        root_module = module.split('.')[0] if module else ""
                        print(f"检查从模块导入: {module} (根模块: {root_module})")

                        # 如果是从 t_ai_agent 模块导入，则允许
                        if root_module == "t_ai_agent":
                            print(f"允许从 t_ai_agent 导入: {module}")
                            # 跳过名称检查，因为是从 t_ai_agent 导入的子模块或函数
                            continue

                        # 检查模块是否在允许列表中
                        if root_module and root_module not in self.allowed_modules:
                            print(f"不允许从该模块导入: {root_module}")
                            return False, f"不允许从模块 '{root_module}' 导入"

                    # 检查直接导入的模块名
                    for name in node.names:
                        module_name = name.name.split('.')[0]
                        print(f"检查导入模块名: {name.name} (根模块: {module_name})")

                        # 如果导入的是 t_ai_agent 或其子模块，则允许
                        if module_name == "t_ai_agent":
                            print(f"允许导入 t_ai_agent 模块: {name.name}")
                            continue

                        # 其他模块必须在允许列表中
                        if module_name not in self.allowed_modules:
                            print(f"模块不在允许列表中: {module_name}")
                            return False, f"不允许导入模块 '{module_name}'"
                        else:
                            print(f"模块允许导入: {module_name}")

                # 检查文件操作
                if isinstance(node, ast.Call):
                    func = node.func
                    if isinstance(func, ast.Name) and func.id == 'open':
                        return False, "不允许文件操作"

                    # 检查 eval/exec
                    if isinstance(func, ast.Name) and func.id in ['eval', 'exec']:
                        return False, "不允许使用 eval() 和 exec() 函数"

                # 检查可能危险的 os/sys 操作
                if isinstance(node, ast.Attribute):
                    if isinstance(node.value, ast.Name):
                        if node.value.id in ['os', 'sys'] and node.attr.startswith('_'):
                            return False, f"不允许访问 '{node.value.id}.{node.attr}'"
            
            return True, ""

        except SyntaxError as e:
            return False, f"语法错误: {str(e)}"

    def save_code_to_file(self, code_str: str, filename: str = None) -> str:
        """
        将提供的代码保存到 t-ai-code-sandbox 目录中的文件。

        Args:
            code_str: 要保存的Python代码
            filename: 可选的自定义文件名，如果为None则生成UUID

        Returns:
            保存文件的路径
        """
        # 定义沙箱目录
        sandbox_dir = "/Users/<USER>/Development/Repositories/terminus/t-ai2/packages/t-ai-code-sandbox"

        # 确保目录存在
        os.makedirs(sandbox_dir, exist_ok=True)

        # 如果未提供文件名则生成一个
        if not filename:
            filename = f"sandbox_{uuid.uuid4().hex}.py"
        elif not filename.endswith('.py'):
            filename = f"{filename}.py"

        filepath = os.path.join(sandbox_dir, filename)

        # 将代码保存到文件
        with open(filepath, 'w') as f:
            f.write(code_str)

        return filepath
    
    def _limit_resources(self):
        """设置执行的资源限制。"""
        # 设置CPU时间限制（转换为整数用于resource.setrlimit）
        cpu_limit = int(self.time_limit)
        resource.setrlimit(resource.RLIMIT_CPU, (cpu_limit, cpu_limit))

        # 设置内存限制
        resource.setrlimit(resource.RLIMIT_AS, (self.memory_limit, self.memory_limit))

    def run_code(self, code_str: str, arguments: Any = None) -> CodeExecutionResult:
        """
        在沙箱环境中执行提供的Python代码。

        Args:
            code_str: 要执行的Python代码字符串
            arguments: 传递给代码执行上下文的可选参数

        Returns:
            包含执行结果的CodeExecutionResult对象
        """
        is_safe, error_msg = self._is_safe_code(code_str)
        if not is_safe:
            return CodeExecutionResult(
                success=False,
                error=f"提供的代码包含不安全操作: {error_msg}",
            )

        # 捕获标准输出和错误输出
        stdout = io.StringIO()
        stderr = io.StringIO()

        # 在try块之前定义变量
        result = None
        start_time = time.time()

        try:
            # 在捕获输出的情况下执行代码
            with contextlib.redirect_stdout(stdout), contextlib.redirect_stderr(stderr):
                # 编译代码
                compiled_code = compile(code_str, "<string>", "exec")

                # 如果提供了参数，则在局部作用域中使其可用
                if arguments is not None:
                    self.locals_dict['arguments'] = arguments

                # 执行代码
                exec(compiled_code, self.globals_dict, self.locals_dict)

                # 如果定义了result变量，则返回它
                if "result" in self.locals_dict:
                    result = self.locals_dict["result"]

            # 执行成功
            output = stdout.getvalue()
            if stderr.getvalue():
                output += "\n--- 错误/警告 ---\n" + stderr.getvalue()

            # 计算执行时间
            execution_time = time.time() - start_time

            # 返回成功、结果和任何输出
            return CodeExecutionResult(
                success=True,
                result=result,
                output=output,
                execution_time=execution_time,
                temp_file=None,  # 直接代码执行没有临时文件
            )
            
        except Exception as e:
            # 捕获回溯信息
            error_msg = f"错误: {str(e)}\n{stderr.getvalue()}\n{traceback.format_exc()}"
            execution_time = time.time() - start_time
            return CodeExecutionResult(
                success=False,
                error=error_msg,
                execution_time=execution_time,
                temp_file=None,  # 直接代码执行没有临时文件
            )

    def run_file(self, file_path: str) -> CodeExecutionResult:
        """
        从文件执行Python代码。

        Args:
            file_path: 要执行的Python文件路径

        Returns:
            包含执行结果的CodeExecutionResult对象
        """
        try:
            with open(file_path, 'r') as file:
                code_str = file.read()
            result = self.run_code(code_str)
            result.temp_file = file_path
            return result
        except FileNotFoundError:
            return CodeExecutionResult(success=False, error=f"文件未找到: {file_path}")
        except Exception as e:
            return CodeExecutionResult(success=False, error=f"读取文件错误: {str(e)}")

    def execute_from_api(self, code_str: str, save_to_file: bool = True, filename: str = None) -> CodeExecutionResult:
        """
        执行通过API提供的代码，可选择先保存到文件。

        Args:
            code_str: 要执行的Python代码
            save_to_file: 是否先将代码保存到文件
            filename: 可选的自定义文件名（仅在save_to_file为True时使用）

        Returns:
            包含执行结果的CodeExecutionResult对象
        """
        if save_to_file:
            # 先将代码保存到文件
            temp_file = self.save_code_to_file(code_str, filename)
            result = self.run_file(temp_file)
            return result
        else:
            # 直接执行代码而不保存到文件
            return self.run_code(code_str)
