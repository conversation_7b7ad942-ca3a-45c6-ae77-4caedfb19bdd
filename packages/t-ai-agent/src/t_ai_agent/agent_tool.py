import json
import time
from typing import List, Optional

from agents import Agent, FunctionTool, Tool
from agents.mcp import MCPServer
from agents.tool_context import ToolContext
from loguru import logger

from t_ai_app.ctx.req_ctx import ReqCtx
from t_ai_app.g import G
from t_ai_common.utils.common import is_sys_service, get_module_key, get_service_alias_key
from t_ai_mcp import TMCPServer, TMCPServerConfig, MCPTool
from t_ai_mcp.server import TMCPSharedServer
from .agent_knowledge import create_knowledge_base_tool
from .agent_trigger import get_skill_tools_from_trigger
from .model.agent_context import AgentContext, AgentExecutionContext, ToolCallMetadata
from .model.agent_dsl import (
    AgentMeta,
    FieldMeta,
    HttpTool,
    McpTool,
    ServiceTool,
    SubTools, ProprietaryTool,
)
from .tool.http_tool_execution import HttpToolExecution
from .tool.proprietary_tool_execution import ProprietaryToolExecution
from .utils.json_util import safe_json_loads
from .utils.params_mapping_helper import (
    create_context_mapping_prompt,
    map_context_to_params,
    need_to_expand,
)
from .utils.trantor_helper import (
    const_value,
    get_mcp_built_in_sse_endpoint,
    is_const_value,
    to_trantor_headers,
    call_service,
    dynamic_value,
    is_var_value,
    call_service_sse,
)

# 基本类型映射
FIELD_TYPE_MAPPING = {
    "Text": "string",
    "RichText": "string",
    "Email": "string",
    "Enum": "string",
    "Attachment": "string",
    "Time": "string",
    "Number": "number",
    "DateTime": "number",
    "Boolean": "boolean",
}


def get_max_tokens_from_context(agent_exec_ctx: AgentExecutionContext) -> Optional[int]:
    def extract_max_tokens(agent_meta: AgentMeta) -> Optional[int]:
        llm_model = agent_meta.props.model
        if not llm_model:
            return None

        setting = llm_model.get("setting") if isinstance(llm_model, dict) else getattr(llm_model, "setting", None)
        if not setting:
            return None

        return setting.get("max_tokens") if isinstance(setting, dict) else getattr(setting, "max_tokens", None)

    # 优先使用当前 agent 的 max_tokens
    if agent_exec_ctx.current_agent:
        # 搜索当前agent对应的元数据
        current_agent: Agent = agent_exec_ctx.current_agent
        current_agent_meta = None
        for agent_meta_item in agent_exec_ctx.agents:
            if agent_meta_item.name == current_agent.name:
                current_agent_meta = agent_meta_item

        if current_agent_meta is not None:
            max_tokens = extract_max_tokens(current_agent_meta)
            if max_tokens is not None:
                return max_tokens

    # 否则取所有 agents 中最小的有效 max_tokens
    token_values = [v for v in (extract_max_tokens(agent) for agent in agent_exec_ctx.agents) if v is not None]
    if token_values:
        return min(token_values)

    return None


def truncate_data_if_token_exceed(result, agent_exec_ctx: AgentExecutionContext, estimate_fn, logger):
    def find_array_data(d):
        if not isinstance(d, dict):
            return None, None

        data = d.get("data")
        if isinstance(data, list):
            return data, d
        if isinstance(data, dict):
            nested_data = data.get("data")
            if isinstance(nested_data, list):
                return nested_data, data

        return None, None

    data_array, parent_obj = find_array_data(result)
    if data_array is None or parent_obj is None:
        return result  # 没找到合适的数组，直接返回

    if not data_array:  # 空数组直接返回
        return result

    try:
        max_token = get_max_tokens_from_context(agent_exec_ctx)
        json_str = json.dumps(result, ensure_ascii=False, separators=(",", ":"))
        estimated_tokens = estimate_fn(json_str)
    except Exception as e:
        import traceback

        stack_trace = traceback.format_exc()
        logger.error(f"JSON 序列化或 Token 估算失败: {e}")
        logger.error(f"完整异常堆栈:\n{stack_trace}")
        return result

    if estimated_tokens > max_token:
        original_len = len(data_array)
        avg_token_per_item = estimated_tokens // max(original_len, 1)
        allowed_items = max_token // max(avg_token_per_item, 1)
        parent_obj["data"] = data_array[:allowed_items]

        logger.warning(
            f"预估 Token 超限：{estimated_tokens} > {max_token}，"
            f"已将 data 截断至前 {allowed_items} 项（原长度: {original_len}）"
        )
    return result


def estimate_token_count(json_str: str) -> int:
    return int(len(json_str) * 0.6)


async def process_field_defaults(
        fields: list[FieldMeta], current_params: dict, agent_exec_ctx: AgentExecutionContext
) -> None:
    """
    递归处理字段默认值

    Args:
        fields: 字段元数据列表
        current_params: 当前参数字典
        agent_exec_ctx: 代理执行上下文，用于动态获取变量值
    """
    if not fields:
        return

    for field in fields:
        # 处理当前字段的默认值
        if (
                field.default_value is not None
                and field.default_value != ""
                and current_params.get(field.field_key, None) is None
        ):
            # 如果默认值是变量，需要动态解析
            if is_var_value(field.default_value):
                try:
                    default_value = await dynamic_value(field.default_value, agent_exec_ctx.variables)
                    current_params[field.field_key] = default_value
                    logger.debug(f"为字段 {field.field_key} 设置动态默认值: {default_value}")
                except Exception as e:
                    logger.warning(f"解析字段 {field.field_key} 的动态默认值失败: {e}")
            else:
                # 静态默认值
                current_params[field.field_key] = field.default_value
                logger.debug(f"为字段 {field.field_key} 设置静态默认值: {field.default_value}")

        # 递归处理子字段
        if field.elements:
            # 如果当前字段在参数中存在，则递归处理其子字段
            if field.field_key in current_params and isinstance(current_params[field.field_key], dict):
                await process_field_defaults(field.elements, current_params[field.field_key], agent_exec_ctx)
            # 如果当前字段不在参数中，但子字段有默认值，则创建父字段并处理子字段
            elif field.field_key not in current_params:
                # 检查子字段是否有默认值
                has_default_in_children = any(
                    child.default_value is not None and child.default_value != "" for child in field.elements
                )
                if has_default_in_children:
                    # 创建父字段对象
                    current_params[field.field_key] = {}
                    await process_field_defaults(field.elements, current_params[field.field_key], agent_exec_ctx)


async def create_http_tool(agent_meta: AgentMeta, tool_config: HttpTool) -> Tool:
    # 创建HTTP工具
    http_execution = HttpToolExecution(tool_config, agent_meta.name)
    return await http_execution.build_tool(agent_meta)


def create_service_run_function(input_meta: list[FieldMeta] | None, service_key: str, agent_name: str, sse_mode: bool):
    async def run_function(context: ToolContext, params_str: str) -> str:
        logger.debug(
            f"run_function invoke : service_key: {service_key}, params_str: {params_str}, input_meta: {input_meta}"
        )

        too_call_id = context.tool_call_id
        agent_exec_ctx: AgentExecutionContext = context.context
        tool_key = get_service_alias_key(service_key)

        # 这里手动添加工具信息，是因为当agent_as_tools后，工具的信息无法透出
        agent_exec_ctx.manual_add_tool_call_mapping(
            too_call_id,
            ToolCallMetadata(key=tool_key, arguments=params_str, create_time=time.time()),
            agent_name
        )

        try:
            model_key = None
            final_service_key = service_key
            if is_sys_service(service_key):
                logger.debug(f"is_sys_service: {service_key}")
                params = safe_json_loads(params_str)
                model_key = params.get("modelKey")
                final_service_key = final_service_key + "?modelKey=" + model_key

            # 如果 input_meta 包含 ConditionGroup、Pageable 或 Model 类型字段，则需要扩展
            if input_meta and need_to_expand(input_meta):
                # 示例：获取对话历史
                conversation_history = agent_exec_ctx.get_conversation_history()

                tool_metadata = agent_exec_ctx.find_tool_in_agent(tool_key, agent_name)

                context_mapping_prompt = create_context_mapping_prompt(
                    service_key, model_key, conversation_history, input_meta, params_str, tool_metadata
                )

                logger.debug(f"context_mapping_prompt: {context_mapping_prompt}")

                # 使用大模型进行映射
                params = await map_context_to_params(agent_exec_ctx, context_mapping_prompt)

                logger.debug(f"mapped_params: {params}")
            else:
                params = safe_json_loads(params_str)

            # 处理字段的默认值
            if input_meta is not None:
                await process_field_defaults(input_meta, params, agent_exec_ctx)

            # 将映射后的参数重置到tool_call当中
            agent_exec_ctx.update_tool_call_arguments(too_call_id, json.dumps(params))

            # 判断是否是SSE模式的编排服务，如果是，则需要使用SSE模式调用
            if sse_mode:
                result = await call_service_sse(service_key=final_service_key, params=params)
            else:
                result = await call_service(service_key=final_service_key, params=params)

            if isinstance(result, dict):
                # 即使是HTTP 200 OK，也需要判断一下返回的Response Body中success的属性字段
                if result.get("success") is not None and result.get("success") is not True:
                    agent_exec_ctx.update_tool_call_status(too_call_id, "error", json.dumps(result.get("err")))

            result = truncate_data_if_token_exceed(result, agent_exec_ctx, estimate_token_count, logger)
            if isinstance(result, str):
                final_result = result
            else:
                final_result = json.dumps(result, ensure_ascii=False, separators=(",", ":"))

            # 更新工具调用状态为成功
            agent_exec_ctx.update_tool_call_status(too_call_id, "success", final_result)

            return final_result
        except Exception as e:
            import traceback

            stack_trace = traceback.format_exc()
            logger.exception(f"调用服务异常，可能是参数映射结果解析失败: {e}")
            logger.error(f"完整异常堆栈:\n{stack_trace}")

            error_result = f"Server error: {str(e)}"
            # 更新工具调用状态为错误
            agent_exec_ctx.update_tool_call_status(too_call_id, "error", error_result)
            return error_result

    return run_function


async def create_service_tool(agent_meta: AgentMeta, tool_config: ServiceTool) -> Tool:
    service_key = tool_config.key
    tool_name = get_service_alias_key(service_key)
    tool_desc = tool_config.desc if tool_config.desc and len(tool_config.desc) > 0 else tool_config.name
    params_schema = await convert_to_params_json_schema(tool_config.input)
    return FunctionTool(
        name=tool_name,
        description=tool_desc,
        params_json_schema=params_schema,
        on_invoke_tool=create_service_run_function(tool_config.input, service_key, agent_meta.name,
                                                   tool_config.sse_mode or False),
        strict_json_schema=True,
    )


async def convert_sub_tool_to_mcp_tool(sub_tool: SubTools, agent_meta: AgentMeta) -> MCPTool:
    return MCPTool(
        name=sub_tool.name,
        description=sub_tool.desc,
        inputSchema=await convert_to_params_json_schema(sub_tool.input),
        _meta={"agent_name": agent_meta.name}
    )


async def create_mcp_server(agent_meta: AgentMeta, tool_config: McpTool) -> MCPServer:
    picked_tools = [await convert_sub_tool_to_mcp_tool(sub_tool, agent_meta) for sub_tool in tool_config.tools]

    server_endpoint = tool_config.mcp_server_endpoint
    match tool_config.source_type:
        case "built-in":
            server_endpoint = await get_mcp_built_in_sse_endpoint()
            if server_endpoint is None:
                callback_url = ReqCtx.get_callback_url()
                if callback_url:
                    server_endpoint = callback_url + "/api/trantor/mcp/built-in/sse"
                else:
                    server_endpoint = tool_config.mcp_server_endpoint
            return TMCPServer(
                tool_config.name,
                TMCPServerConfig(
                    server_endpoint=server_endpoint,
                    headers_factory=lambda: {**to_trantor_headers()},
                    list_tools=picked_tools,
                    lazy=tool_config.lazy,
                ))
        case _:
            server_args = {
                "model_publisher": tool_config.model_publisher,
                "model_name": tool_config.model_name,
                "instructions": tool_config.instructions,
            }
            return TMCPSharedServer(
                tool_config.name,
                TMCPServerConfig(
                    server_endpoint=server_endpoint,
                    server_args=server_args,
                    list_tools=picked_tools,
                    lazy=tool_config.lazy,
                    headers_factory = lambda: {
                        "Authorization": G.APP_SETTING.ai_proxy.admin_authorization,
                    },
                ))


async def create_proprietary_tool(agent_meta: AgentMeta, tool_config: ProprietaryTool) -> Tool:
    # 创建专有工具
    proprietary_execution = ProprietaryToolExecution(tool_config, agent_meta.name)
    return await proprietary_execution.build_tool(agent_meta)


async def build_field_schema(_field: FieldMeta, agent_exec_ctx: AgentExecutionContext | None = None) -> dict:
    field_type = _field.field_type

    if field_type == "Array":
        items_schema = {}

        if _field.element:
            element_schema = await build_field_schema(_field.element, agent_exec_ctx)
            items_schema = {k: v for k, v in element_schema.items() if k not in ["title", "description"]}

        schema = {
            "type": "array",
            "title": _field.field_name,
            "description": _field.description or "",
            "items": items_schema,
        }
    elif field_type in [
        "Model",
        "Object",
        "Pageable",
        "Paging",
        "ConditionGroup",
        "ConditionItems",
    ]:
        properties = {}
        required_fields = []
        if _field.elements:
            for element in _field.elements:
                properties[element.field_key] = await build_field_schema(element, agent_exec_ctx)
                if element.required:
                    required_fields.append(element.field_key)

        schema = {
            "type": "object",
            "title": _field.field_name,
            "description": _field.description or "",
            "properties": properties,
            "additionalProperties": False,
        }
        if len(required_fields) > 0:
            schema["required"] = required_fields
    else:
        schema = {
            "type": FIELD_TYPE_MAPPING.get(field_type, "string"),
            "title": _field.field_name,
            "description": _field.description or "",
        }

    # 处理默认值
    if _field.default_value is not None:
        if is_var_value(_field.default_value):
            if is_const_value(_field.default_value):
                schema["default"] = await const_value(_field.default_value)  # type: ignore
            elif agent_exec_ctx is not None:
                schema["default"] = await dynamic_value(_field.default_value, agent_exec_ctx.variables)
        else:
            schema["default"] = _field.default_value

    return schema


async def convert_to_params_json_schema(
        input_meta: list[FieldMeta] | None, agent_exec_ctx: AgentExecutionContext | None = None
) -> dict:
    """
    将 trantor 的 tool_params 转换为 json_schema

    :param input_meta: 参数结构
    :argument agent_exec_ctx: 执行上下文，用于动态获取默认值
    """
    params_schema = {
        "type": "object",
        "properties": {},
        "required": [],
        "additionalProperties": False,
    }
    if input_meta:
        for field in input_meta:
            field_key = field.field_key
            params_schema["properties"][field_key] = await build_field_schema(field, agent_exec_ctx)
            if field.required:
                params_schema["required"].append(field_key)
    # prepare_schema_for_openai(params_schema)
    return params_schema


async def handle_tools(
        agent_meta: AgentMeta,
        agent_ctx: AgentContext
) -> tuple[List[Tool], List[MCPServer]]:
    empty_tools = True
    empty_knowledge_bases = True

    skill_tools_config = agent_meta.props.skill_tools or []

    if skill_tools_config and len(skill_tools_config) > 0:
        empty_tools = False

    if agent_meta.props.knowledge_base and len(agent_meta.props.knowledge_base.knowledge_bases) > 0:
        empty_knowledge_bases = False

    if empty_tools and empty_knowledge_bases:
        return [], []

    skill_tools = skill_tools_config.copy()
    tools: List[Tool] = []
    mcp_servers: List[MCPServer] = []

    # 工具的生成以触发器中的配置为准
    if agent_ctx.trigger is not None:
        skill_tools = get_skill_tools_from_trigger(agent_ctx, agent_meta) or []

    for tool_config in skill_tools:
        _tool, _mcp_server = None, None
        tool_visible = None
        match tool_config:
            case HttpTool() as http_tool_config:  # match on class pattern
                tool_visible = http_tool_config.tool_visible
                _tool = await create_http_tool(agent_meta, http_tool_config)
                tools.append(_tool)
            case ServiceTool() as service_tool_config:
                tool_visible = service_tool_config.tool_visible
                _tool = await create_service_tool(agent_meta, service_tool_config)
                tools.append(_tool)
            case McpTool() as mcp_tool_config:
                _mcp_server = await create_mcp_server(agent_meta, mcp_tool_config)
                mcp_servers.append(_mcp_server)
            case ProprietaryTool() as proprietary_tool_config:
                tool_visible = proprietary_tool_config.tool_visible
                _tool = await create_proprietary_tool(agent_meta, proprietary_tool_config)
                tools.append(_tool)
            case _:
                raise ValueError(f"未知工具类型: {tool_config.model_dump_json()}")
        if _tool:
            # tool信息注册到上下文中
            # 针对编排服务，取一下编排服务的模块前缀，MCP或者Http的值为""
            agent_ctx.add_tool(
                module_key=get_module_key(tool_config.key),
                tool_key=get_service_alias_key(tool_config.key),
                tool_name=tool_config.name,
                tool_visible=tool_visible,
                final_output=tool_config.final_output,  # type: ignore
                tool_config=tool_config,
                agent_name=agent_meta.name,
            )
        if _mcp_server:
            agent_ctx.add_mcp_server(_mcp_server)
            # 只有 McpTool 类型才有 tools 属性
            if isinstance(tool_config, McpTool) and tool_config.tools is not None:
                for sub_tool in tool_config.tools:
                    agent_ctx.add_tool(
                        module_key=get_module_key(sub_tool.key),
                        tool_key=get_service_alias_key(sub_tool.key),
                        tool_name=sub_tool.alias_name if sub_tool.alias_name else sub_tool.name,
                        tool_visible=sub_tool.tool_visible,
                        final_output=sub_tool.final_output,
                        tool_config=sub_tool,
                        agent_name=agent_meta.name,
                    )

    # 如果配置了知识库，并起挂载了知识库文档，则就需要添加知识库搜索工具
    if agent_meta.props.knowledge_base and len(agent_meta.props.knowledge_base.knowledge_bases) > 0:
        try:
            knowledge_base_tool = create_knowledge_base_tool(agent_meta.props.knowledge_base)
            tools.append(knowledge_base_tool)

            # 注册知识库工具到上下文
            agent_ctx.add_tool(tool_key="knowledge_base_search", tool_name="知识库搜索", agent_name=agent_meta.name)

            logger.info("已为Agent添加知识库搜索工具")
        except Exception as e:
            logger.error(f"创建知识库工具失败: {e}")

    # 如果配置了自定义工具链，则需要挂载该工具
    if agent_meta.props.enabled_custom_tool_chain:
        tools.append()

    return tools, mcp_servers
