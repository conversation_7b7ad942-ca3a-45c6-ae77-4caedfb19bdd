default_lang = "zh-CN"
message_i18n: dict[str, dict[str, str]] = {
    "handoff_content": {
        "zh-CN": "👉 转交至 {target_agent_name} 为您服务",
        "en-US": "👉 Handed over to {target_agent_name} for you",
    },
    "as_tool_handoff_content": {
        "zh-CN": "👉 由 {target_agent_name} 辅助处理",
        "en-US": "👉 Assisted by {target_agent_name}",
    },
    "as_tool_handoff_feedback_content": {
        "zh-CN": "👉 反馈给 {target_agent_name} 继续为您处理",
        "en-US": "👉 Return to {target_agent_name} to continue processing for you",
    },
    "tool_name_knowledge_base_search": {
        "zh-CN": "知识库搜索",
        "en-US": "Knowledge Base Search",
    },
    "error_content": {
        "zh-CN": "AI开小差，请重试尝试",
        "en-US": "The AI is a little different, please try again",
    },
}


def get_i18n_message(key: str, lang: str | None, **kwargs) -> str:
    template = message_i18n.get(key, {}).get(
        lang or default_lang,
        message_i18n.get(key, {}).get(default_lang, '')
    )
    return template.format(**kwargs)
