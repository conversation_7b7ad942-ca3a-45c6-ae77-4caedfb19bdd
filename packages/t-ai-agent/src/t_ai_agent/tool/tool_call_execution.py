from abc import ABC, abstractmethod
from typing import Any

from agents import Tool
from pydantic import BaseModel, ConfigDict

from t_ai_agent.model import AgentExecutionContext
from t_ai_agent.model.agent_dsl import FieldMeta, AgentMeta
from t_ai_agent.utils.trantor_helper import is_var_value, is_const_value, const_value, dynamic_value


# 基本类型映射
FIELD_TYPE_MAPPING = {
    "Text": "string",
    "RichText": "string",
    "Email": "string",
    "Enum": "string",
    "Attachment": "string",
    "Time": "string",
    "Number": "number",
    "DateTime": "number",
    "Boolean": "boolean",
}


class ToolCallExecutionModel(BaseModel):
    model_config = ConfigDict(
        populate_by_name=True,
        validate_default=True,
        extra="ignore",
        validate_assignment=True,
    )


class ToolCallExecution(ABC):
    """工具调用执行的抽象基类"""
    
    @abstractmethod
    def build_tool(self, agent_meta: AgentMeta) -> Tool:
        """构建工具的JSON Schema"""
        pass

    @abstractmethod
    def execute(self, agent_name: str) -> Any:
        """执行工具调用"""
        pass

    async def build_field_schema(self, _field: FieldMeta, agent_exec_ctx: AgentExecutionContext | None = None) -> dict:
        field_type = _field.field_type

        if field_type == "Array":
            items_schema = {}

            if _field.element:
                element_schema = await self.build_field_schema(_field.element, agent_exec_ctx)
                items_schema = {k: v for k, v in element_schema.items() if k not in ["title", "description"]}

            schema = {
                "type": "array",
                "title": _field.field_name,
                "description": _field.description or "",
                "items": items_schema,
            }
        elif field_type in [
            "Model",
            "Object",
            "Pageable",
            "Paging",
            "ConditionGroup",
            "ConditionItems",
        ]:
            properties = {}
            required_fields = []
            if _field.elements:
                for element in _field.elements:
                    properties[element.field_key] = await self.build_field_schema(element, agent_exec_ctx)
                    if element.required:
                        required_fields.append(element.field_key)

            schema = {
                "type": "object",
                "title": _field.field_name,
                "description": _field.description or "",
                "properties": properties,
                "additionalProperties": False,
            }
            if len(required_fields) > 0:
                schema["required"] = required_fields
        else:
            schema = {
                "type": FIELD_TYPE_MAPPING.get(field_type, "string"),
                "title": _field.field_name,
                "description": _field.description or "",
            }

        # 处理默认值
        if _field.default_value is not None:
            if is_var_value(_field.default_value):
                if is_const_value(_field.default_value):
                    schema["default"] = await const_value(_field.default_value)  # type: ignore
                elif agent_exec_ctx is not None:
                    schema["default"] = await dynamic_value(_field.default_value, agent_exec_ctx.variables)
            else:
                schema["default"] = _field.default_value

        return schema


    async def convert_to_params_json_schema(self, input_meta: list[FieldMeta] | None,
                                            agent_exec_ctx: AgentExecutionContext | None = None) -> dict:
        """
        将 trantor 的 tool_params 转换为 json_schema

        :param input_meta: 参数结构
        :argument agent_exec_ctx: 执行上下文，用于动态获取默认值
        """
        params_schema = {
            "type": "object",
            "properties": {},
            "required": [],
            "additionalProperties": False,
        }
        if input_meta:
            for field in input_meta:
                field_key = field.field_key
                params_schema["properties"][field_key] = await self.build_field_schema(field, agent_exec_ctx)
                if field.required:
                    params_schema["required"].append(field_key)
        # prepare_schema_for_openai(params_schema)
        return params_schema
