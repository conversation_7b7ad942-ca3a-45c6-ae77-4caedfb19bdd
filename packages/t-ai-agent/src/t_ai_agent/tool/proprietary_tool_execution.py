import json
import time
from typing import Any, Optional

import httpx
from agents import FunctionTool
from agents.tool_context import Tool<PERSON>ontext
from loguru import logger
from pydantic import Field

from t_ai_app.ctx import ReqCtx
from t_ai_common.utils.common import get_service_alias_key
from .tool_call_execution import Too<PERSON><PERSON>allExecution, ToolCallExecutionModel
# from ..agent_tool import truncate_data_if_token_exceed, estimate_token_count
from ..model import AgentExecutionContext
from ..model.agent_context import ToolCallMetadata
from ..model.agent_dsl import FieldMeta, ProprietaryTool, AgentMeta
from ..utils.json_util import safe_json_loads
from ..utils.trantor_helper import dynamic_value
from ..utils.proprietary_tool_cache import proprietary_tool_cache
from ..sandbox.sandbox_service import run_sandbox
from ..sandbox.model import RunSandboxRequest


class ProprietaryToolDefinition(ToolCallExecutionModel):
    description: Optional[str] = None
    implementation: Optional[Implementation] = Field(None, alias="implementation"),
    tool_schema: Optional[ToolSchema] = Field(None, alias="toolSchema"),

    class Implementation(ToolCallExecutionModel):
        code: Optional[str] = None
        encoding: Optional[str] = Field("base64", alias="encoding"),
        language: Optional[str] = Field("python", alias="language")

    class ToolSchema(ToolCallExecutionModel):
        properties: Optional[dict[str, Any]] = Field(None, alias="properties")
        required: Optional[list[str]] = Field(None, alias="required")
        type: Optional[str] = Field("object", alias="type")


class ProprietaryToolExecution(ToolCallExecution):
    """专有工具执行类"""
    proprietary_tool: ProprietaryTool
    agent_name: str

    def __init__(self, proprietary_tool: ProprietaryTool, agent_name: str):
        self.proprietary_tool = proprietary_tool
        self.agent_name = agent_name

    async def build_tool(self, agent_meta: AgentMeta) -> FunctionTool:
        return FunctionTool(
            name=self.proprietary_tool.key,
            description=self.proprietary_tool.desc or self.proprietary_tool.name,
            params_json_schema=await build_proprietary_tool_json_schema(self.proprietary_tool),
            on_invoke_tool=self.execute(agent_meta.name),
            strict_json_schema=True,
        )


    def execute(self, agent_name: str) -> Any:
        async def run_function(context: ToolContext, params_str=None) -> str:
            logger.debug(f"proprietary_run_function invoke with params: {params_str}")

            tool_config = self.proprietary_tool
            too_call_id = context.tool_call_id
            agent_exec_ctx: AgentExecutionContext = context.context
            tool_key = tool_config.key

            try:
                # agent:proprietary_tool:PJ0724:ERP_FIN$agent_reimbursement:440724569185477:check_expense_report
                current_agent_meta: AgentMeta = agent_exec_ctx.get_agent_meta_by_name(agent_exec_ctx.current_agent.name)
                cache_key = f"agent:proprietary_tool:{ReqCtx.get_team_code()}:{ReqCtx.get_user_id()}:{current_agent_meta.key}:{tool_key}"

                # 从redis中加载专有工具定义
                tool_definition = proprietary_tool_cache.get_tool_definition(cache_key)
                if tool_definition is None:
                    err_result = f"未找到专有工具定义，cache_key: {cache_key}"
                    logger.error(err_result)
                    agent_exec_ctx.update_tool_call_status(too_call_id, "error", err_result)
                    return err_result

                # 获取解码后的Python代码
                python_code = proprietary_tool_cache.get_decoded_code(tool_definition)
                if python_code is None:
                    err_result = f"无法获取或解码Python代码，cache_key: {cache_key}"
                    logger.error(err_result)
                    agent_exec_ctx.update_tool_call_status(too_call_id, "error", err_result)
                    return err_result

                logger.debug(f"获取到Python代码，长度: {len(python_code)}")

                # 解析工具调用参数
                tool_params = {}
                if params_str:
                    tool_params = safe_json_loads(params_str)

                # 创建沙箱执行请求
                sandbox_request = RunSandboxRequest(
                    code=python_code,
                    arguments=tool_params
                )

                # 在沙箱中运行Python代码
                execution_result = await run_sandbox(sandbox_request)

                if execution_result.get("success", False):
                    result = execution_result.get("result", "")
                    output = execution_result.get("output", "")
                    execution_time = execution_result.get("execution_time", 0)

                    logger.info(f"专有工具执行成功，耗时: {execution_time:.3f}秒")

                    # 更新工具调用状态为成功
                    agent_exec_ctx.update_tool_call_status(too_call_id, "success", str(result))

                    # 返回结果，如果有输出也包含在内
                    if output:
                        return f"执行结果: {result}\n输出: {output}"
                    else:
                        return str(result)
                else:
                    error_msg = execution_result.get("error", "未知错误")
                    execution_time = execution_result.get("execution_time", 0)

                    logger.error(f"专有工具执行失败，耗时: {execution_time:.3f}秒，错误: {error_msg}")

                    # 更新工具调用状态为错误
                    agent_exec_ctx.update_tool_call_status(too_call_id, "error", error_msg)

                    return f"执行失败: {error_msg}"
            except Exception as e:
                import traceback

                stack_trace = traceback.format_exc()
                logger.exception(f"专有工具调用异常: {e}")
                logger.error(f"完整异常堆栈:\n{stack_trace}")

                err_result = f"Error executing Proprietary tool: {str(e)}"

                # 更新工具调用状态为错误
                agent_exec_ctx.update_tool_call_status(too_call_id, "error", err_result)

                return err_result
        return run_function


async def build_proprietary_tool_json_schema(proprietary_tool: ProprietaryTool) -> dict:
    schema = {
        "type": "object",
        "required": [],
        "additionalProperties": False,
    }
    schema.update(proprietary_tool.tool_schema)
    return schema
