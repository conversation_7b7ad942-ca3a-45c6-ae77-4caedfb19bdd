import json
import time
from typing import Any, Optional

import httpx
from agents import FunctionTool
from agents.tool_context import Tool<PERSON>ontext
from loguru import logger

from t_ai_common.utils.common import get_service_alias_key
from .tool_call_execution import Too<PERSON><PERSON><PERSON>Execution, ToolCallExecutionModel
# from ..agent_tool import truncate_data_if_token_exceed, estimate_token_count
from ..model import AgentExecutionContext
from ..model.agent_context import ToolCallMetadata
from ..model.agent_dsl import FieldMeta, HttpTool, AgentMeta
from ..utils.json_util import safe_json_loads
from ..utils.trantor_helper import dynamic_value


class HttpToolInvokeParams(ToolCallExecutionModel):
    headers: Optional[dict[str, Any]]
    parameter: Optional[dict[str, Any]]
    body: Optional[dict[str, Any]]


class HttpToolExecution(ToolCallExecution):
    """HTTP工具执行类"""
    http_tool: HttpTool
    agent_name: str

    def __init__(self, http_tool: HttpTool, agent_name: str):
        self.http_tool = http_tool
        self.agent_name = agent_name

    async def build_tool(self, agent_meta: AgentMeta) -> FunctionTool:
        return FunctionTool(
            name=get_service_alias_key(self.http_tool.key),
            description=self.http_tool.desc or self.http_tool.name,
            params_json_schema=await self.convert_to_params_json_schema(build_http_tool_json_schema(self.http_tool)),
            on_invoke_tool=self.execute(agent_meta.name),
            strict_json_schema=True,
        )


    def execute(self, agent_name: str) -> Any:
        async def run_function(context: ToolContext, params_str=None) -> str:
            logger.debug(f"http_run_function invoke with params: {params_str}")

            tool_config = self.http_tool
            too_call_id = context.tool_call_id
            agent_exec_ctx: AgentExecutionContext = context.context
            tool_key = tool_config.key

            # 这里手动添加工具信息，是因为当agent_as_tools后，工具的信息无法透出
            agent_exec_ctx.manual_add_tool_call_mapping(
                too_call_id, ToolCallMetadata(key=tool_key, arguments=params_str, create_time=time.time()), agent_name
            )

            try:
                # 解析HTTP工具配置
                http_tool_invoke_params = HttpToolInvokeParams.model_validate(safe_json_loads(params_str))

                http_headers = await get_http_headers(agent_exec_ctx, tool_config, http_tool_invoke_params)

                http_params = await get_http_parameter(agent_exec_ctx, tool_config, http_tool_invoke_params)

                http_path_variables = await get_http_path_variables(agent_exec_ctx, tool_config,
                                                                    http_tool_invoke_params)

                http_body = await get_http_body(agent_exec_ctx, tool_config, http_tool_invoke_params)

                url = tool_config.url
                method = tool_config.method.upper()
                content_type = tool_config.content_type

                # 处理URL中的路径变量
                if http_path_variables and isinstance(http_params, dict):
                    for var_name, var_value in http_path_variables.items():
                        if var_name in http_params:
                            # 替换URL中的路径变量 {var_name}
                            url = url.replace(f"{{{var_name}}}", str(http_params[var_name]))
                            # 从请求参数中移除已处理的路径变量
                            del http_params[var_name]

                if not url or not method:
                    raise ValueError("URL and method are required")

                req_timeout = 1000 * 60 * 3
                async with httpx.AsyncClient() as client:
                    logger.info(f"Sending {method} request to {url} with headers: {http_headers}, params: {http_params}, body: {http_body}")
                    if method == "GET":
                        response = await client.get(url, headers=http_headers, params=http_params)
                    elif method == "POST":
                        if content_type.upper() == "APPLICATION/JSON":
                            response = await client.post(url,
                                                         headers=http_headers,
                                                         params=http_params,
                                                         json=http_body,
                                                         timeout=req_timeout)
                        else:
                            response = await client.post(url,
                                                         headers=http_headers,
                                                         params=http_params,
                                                         data=http_body,
                                                         timeout=req_timeout)
                    elif method == "PUT":
                        if content_type.upper() == "APPLICATION/JSON":
                            response = await client.put(url,
                                                        headers=http_headers,
                                                        params=http_params,
                                                        json=http_body,
                                                        timeout=req_timeout)
                        else:
                            response = await client.put(url,
                                                        headers=http_headers,
                                                        params=http_params,
                                                        data=http_body,
                                                        timeout=req_timeout)
                    elif method == "DELETE":
                        response = await client.delete(url,
                                                       headers=http_headers,
                                                       params=http_params,
                                                       timeout=req_timeout)
                    else:
                        raise ValueError(f"Unsupported HTTP method: {method}")

                    response.raise_for_status()

                    # 尝试解析响应为JSON，如果失败则返回文本
                    try:
                        result = response.json()
                        logger.info(f"Response from {method} request to {url} with result: {result}")
                    except ValueError:
                        result = {"text": response.text}

                    # 如果响应太大，可能需要截断
                    # FIXME 有循环依赖，依赖解开后再放开
                    # result = truncate_data_if_token_exceed(result, agent_exec_ctx, estimate_token_count, logger)
                    final_result = json.dumps(result, ensure_ascii=False, separators=(",", ":"))

                    # 更新工具调用状态为成功
                    agent_exec_ctx.update_tool_call_status(too_call_id, "success", final_result)

                    return final_result
            except Exception as e:
                import traceback

                stack_trace = traceback.format_exc()
                logger.exception(f"HTTP调用异常: {e}")
                logger.error(f"完整异常堆栈:\n{stack_trace}")

                err_result = f"Error executing HTTP tool: {str(e)}"

                # 更新工具调用状态为错误
                agent_exec_ctx.update_tool_call_status(too_call_id, "error", err_result)

                return err_result

        return run_function


def build_http_tool_json_schema(http_tool: HttpTool) -> list[FieldMeta]:
    """构建HTTP工具的JSON Schema"""
    json_schema = []

    # 构建header部分的schema
    header_schema = build_header_json_schema(http_tool)
    if header_schema:
        json_schema.append(header_schema)

    # 构建parameter部分的schema
    param_schema = build_parameter_json_schema(http_tool)
    if param_schema:
        json_schema.append(param_schema)

    # 构建body部分的schema
    body_schema = build_body_json_schema(http_tool)
    if body_schema:
        json_schema.append(body_schema)

    return json_schema


def build_header_json_schema(http_tool: HttpTool) -> Optional[FieldMeta]:
    """构建HTTP请求头的JSON Schema"""
    if not http_tool.headers:
        return None

    # 创建子字段
    elements = []
    for header in http_tool.headers:
        element = FieldMeta.model_validate({
            "field_key": header.get("key", ""),
            "field_name": header.get("key", ""),
            "field_type": header.get("value", {}).get("fieldType", "Text"),
            "default_value": header.get("value", None),
            "required": False
        })
        elements.append(element)

    header_field = FieldMeta.model_validate({
        "field_key": "headers",
        "field_name": "headers",
        "field_type": "Object",
        "description": "http request headers",
        "elements": elements
    })
    return header_field


def build_parameter_json_schema(http_tool: HttpTool) -> Optional[FieldMeta]:
    """构建HTTP请求参数的JSON Schema"""
    if not http_tool.params:
        return None

    # 创建子字段
    elements = []
    for param in http_tool.params:
        element = FieldMeta.model_validate({
            "field_key": param.get("key", ""),
            "field_name": param.get("key", ""),
            "field_type": param.get("value", {}).get("fieldType", "Text"),
            "defaultValue": param.get("value", None),
            "required": False
        })
        elements.append(element)

    param_field = FieldMeta.model_validate({
        "field_key": "parameter",
        "field_name": "parameter",
        "field_type": "Object",
        "description": "http request parameter",
        "elements": elements
    })
    return param_field


def build_body_json_schema(http_tool: HttpTool) -> Optional[FieldMeta]:
    """构建HTTP请求体的JSON Schema"""
    if not http_tool.json_body:
        return None

    if http_tool.body_type == "VALUE":
        return FieldMeta.model_validate({
            "field_key": "body",
            "field_name": "body",
            "field_type": "Object",
            "description": "http request body",
            "defaultValue": http_tool.body
        })
    else:
        # 处理jsonBody字段
        elements = []
        if isinstance(http_tool.json_body, list):
            for item in http_tool.json_body:
                elements.append(item)

        body_field = FieldMeta.model_validate({
            "field_key": "body",
            "field_name": "body",
            "field_type": "Object",
            "description": "http request body",
            "elements": elements
        })
        return body_field


async def get_http_headers(agent_exec_ctx: AgentExecutionContext, tool_config: HttpTool, http_tool_invoke_params: HttpToolInvokeParams):
    http_headers = {}
    if http_tool_invoke_params.headers is not None:
        http_headers = http_tool_invoke_params.headers
    for header in tool_config.headers or []:
        header_key = header.get("key", "")
        header_value = await dynamic_value(header.get("value", {}), agent_exec_ctx.variables)
        if header_key != "" and header_value != "" and header_value is not None:
            http_headers.update({header_key: header_value})
    logger.debug(f"HTTP Headers: {http_headers}")
    return http_headers


async def get_http_parameter(agent_exec_ctx: AgentExecutionContext, tool_config: HttpTool, http_tool_invoke_params: HttpToolInvokeParams):
    http_params = {}
    if http_tool_invoke_params.parameter is not None:
        http_params = http_tool_invoke_params.parameter
    for params in tool_config.params or []:
        params_value = await dynamic_value(params.get("value", {}), agent_exec_ctx.variables)
        http_params.update({params.get("key", ""): params_value})
    logger.debug(f"HTTP Params: {http_params}")
    return http_params


async def get_http_path_variables(agent_exec_ctx: AgentExecutionContext, tool_config: HttpTool, http_tool_invoke_params: HttpToolInvokeParams):
    http_path_variables = {}
    for path_variables in tool_config.path_variables or []:
        path_variables_value = await dynamic_value(path_variables.get("value", {}), agent_exec_ctx.variables)
        http_path_variables.update({path_variables.get("key", ""): path_variables_value})
    logger.debug(f"HTTP Path Variables: {http_path_variables}")
    return http_path_variables


async def get_http_body(agent_exec_ctx: AgentExecutionContext, tool_config: HttpTool, http_tool_invoke_params: HttpToolInvokeParams):
    http_body = {}
    if tool_config.body_type == 'JSON':
        if http_tool_invoke_params.body is not None:
            http_body = http_tool_invoke_params.body
            # 填充覆盖默认值(当前只支持一层)
            await fill_body_field_default_value(agent_exec_ctx, http_body, tool_config.json_body)
        else:
            if tool_config.json_body is not None:
                if isinstance(tool_config.json_body, dict):
                    for item in tool_config.json_body:
                        http_body[item.get("key", "")] = await dynamic_value(item.get("value", {}),
                                                                             agent_exec_ctx.variables)
                elif isinstance(tool_config.json_body, str):
                    http_body = safe_json_loads(tool_config.json_body)
    else:
        http_body = await dynamic_value(tool_config.body, agent_exec_ctx.variables)
    logger.debug(f"HTTP Body: {http_body}")
    return http_body


async def fill_body_field_default_value(agent_exec_ctx: AgentExecutionContext, http_body: dict, body_fields: list[FieldMeta]):
    for item in body_fields:
        filed_key = item.field_key
        filed_value = item.default_value
        if filed_key and filed_value and (http_body.get(filed_key) is not None or http_body.get(filed_key) == ""):
            field_default_value = await dynamic_value(filed_value, agent_exec_ctx.variables)
            http_body[filed_key] = field_default_value
            logger.info(f"fill body field: {filed_key} = {field_default_value}")
