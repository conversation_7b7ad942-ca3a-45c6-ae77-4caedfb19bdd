import hashlib
import time
from typing import Awaitable, Callable, List

from agents import (
    Agent,
    FunctionToolResult,
    ModelSettings,
    OpenAIChatCompletionsModel,
    RunContextWrapper,
    ToolsToFinalOutputFunction,
    ToolsToFinalOutputResult,
    handoff, HandoffInputData,
)
from agents.tool_context import ToolContext
from loguru import logger
from t_ai_common.utils.common import get_module_key, get_service_alias_key

from .agent_hooks import CustomAgentHooks

# 知识库相关导入
from .agent_tool import handle_tools
from .ai_proxy_client_factory import ai_proxy_client_factory
from .model.agent_context import AgentContext, AgentExecutionContext
from .model.agent_dsl import (
    AgentElement,
    AgentMeta,
    Handoffs,
    ReferenceAgentMeta,
)
from .utils import input_item_helpers
from .utils.trantor_helper import dynamic_prompt

# 基本类型映射
FIELD_TYPE_MAPPING = {
    "Text": "string",
    "RichText": "string",
    "Email": "string",
    "Enum": "string",
    "Attachment": "string",
    "Time": "string",
    "Number": "number",
    "DateTime": "number",
    "Boolean": "boolean",
}

# Agent cache to avoid recreating the same agents
_agent_cache = {}


def _get_agent_meta_hash(agent_meta):
    """Generate a hash from agent_meta to use as cache key"""
    meta_str = agent_meta.model_dump_json()
    return hashlib.md5(meta_str.encode()).hexdigest()


async def get_agent_from_cache(agent_meta: AgentMeta) -> tuple[Agent, AgentContext]:
    """Function to create an agent with caching support"""
    cache_key = _get_agent_meta_hash(agent_meta)
    if cache_key in _agent_cache:
        logger.debug(f"Using cached agent for {agent_meta.key}")
        return _agent_cache[cache_key]
    agent_ctx: AgentContext = AgentContext(main_agent_lang_switch=agent_meta.props.reply_with_user_language)
    main_agent, agent_ctx = await create_agent(agent_meta, agent_ctx)
    _agent_cache[cache_key] = main_agent, agent_ctx
    return main_agent, agent_ctx


async def create_agent(agent_meta: AgentMeta, agent_ctx: AgentContext) -> tuple[Agent, AgentContext]:
    agents_dict = {}
    handoffs_dict = {}

    await only_create_agent(agent_meta, agents_dict, handoffs_dict, agent_ctx)
    await apply_handoffs(handoffs_dict, agents_dict, agent_ctx)

    agent = agents_dict.get(agent_meta.key)
    if agent is None:
        raise ValueError(f"Agent with key '{agent_meta.key}' not found")

    return agent, agent_ctx


async def only_create_agent(agent_meta: AgentMeta, agents_dict, handoffs_dict, agent_ctx: AgentContext):
    agent_key = agent_meta.key

    # Create the agent
    agent = await create_agent_from_meta(agent_meta, agent_ctx)
    agents_dict[agent_key] = agent

    # Process all child agents recursively
    await process_children(agent_meta, agents_dict, handoffs_dict, agent_ctx)

    # Process all handoffs
    if agent_meta.handoffs:
        handoffs_dict[agent_key] = agent_meta.handoffs


async def process_children(agent_meta: AgentElement, agents_dict, handoffs_dict, agent_ctx):
    if agent_meta.children:
        for child in agent_meta.children:
            if isinstance(child, ReferenceAgentMeta):
                await process_reference_agent(child, agents_dict, handoffs_dict, agent_ctx)
            elif isinstance(child, AgentMeta):
                await process_regular_agent(child, agents_dict, handoffs_dict, agent_ctx)


async def process_regular_agent(agent_meta: AgentMeta, agents_dict, handoffs_dict, agent_ctx):
    """Process a regular Agent and its children"""
    await only_create_agent(agent_meta, agents_dict, handoffs_dict, agent_ctx)


async def process_reference_agent(ref_agent: ReferenceAgentMeta, agents_dict, handoffs_dict, agent_ctx):
    """Process a ReferenceAgent, its target agent, and its own children"""
    ref_key = ref_agent.key
    target_agent = ref_agent.target_agent

    if target_agent is None:
        raise ValueError(f"ReferenceAgent {ref_key} has no target_agent")

    # Create the target agent
    agent, agent_ctx = await create_agent(target_agent, agent_ctx)
    agents_dict[ref_key] = agent

    # Process any children of the ReferenceAgent itself
    await process_children(ref_agent, agents_dict, handoffs_dict, agent_ctx)

    # Handle handoffs from the ReferenceAgent itself
    if ref_agent.handoffs:
        handoffs_dict[ref_key] = ref_agent.handoffs


def handoff_message_filter(handoff_input_data: HandoffInputData) -> HandoffInputData:
    handoff_message_data = input_item_helpers.remove_handoff_tools(handoff_input_data)
    return HandoffInputData(
        input_history=handoff_input_data.input_history,
        pre_handoff_items=tuple(handoff_message_data.pre_handoff_items),
        new_items=tuple(handoff_message_data.new_items),
    )


async def apply_handoffs(handoffs_dict: dict[str, List[Handoffs]], agents_dict, agent_ctx: AgentContext):
    """Apply collected handoffs to their respective agents"""
    for agent_key, handoffs_list in handoffs_dict.items():
        agent_handoffs = []
        agent_tools = []
        for handoff_config in handoffs_list:
            target_key = handoff_config.agent_key
            if target_key in agents_dict:
                target_agent = agents_dict[target_key]
                tool_name = target_agent.name
                # tool_key优先使用agent的key
                agent_meta = agent_ctx.get_agent_meta_by_name(target_agent.name)
                if agent_meta is not None:
                    tool_key = get_service_alias_key(agent_meta.key)
                else:
                    tool_key = get_service_alias_key(target_key)
                module_key = get_module_key(target_key)
                tool_description = handoff_config.handoff_description
                as_tool = handoff_config.feedback_after_completion
                if as_tool:
                    agent_meta = agent_ctx.find_agent_meta(agent_key)
                    agent_ctx.add_tool(
                        module_key=module_key,
                        tool_key=tool_key,
                        tool_name=tool_name,
                        agent_name=agent_meta.name,
                        agent_as_tool=True
                    )
                    agent_tools.append(target_agent.as_tool(tool_key, tool_description))
                else:
                    agent_handoffs.append(
                        handoff(
                            agent=target_agent,
                            tool_name_override=tool_key,
                            tool_description_override=tool_description,
                            input_filter=handoff_message_filter
                        )
                    )
        # 把自己的handoffs也添加到handoffs中
        self_handoffs = agents_dict[agent_key].handoffs
        if self_handoffs:
            existing_names = [h.tool_name for h in agent_handoffs if hasattr(h, "tool_name")]
            for _handoff in self_handoffs:
                # 检查是否已经有同名的handoff
                if not hasattr(_handoff, "tool_name") or _handoff.tool_name not in existing_names:
                    agent_handoffs.append(_handoff)

        # 把自己的tools也添加到agent_tools中
        self_tools = agents_dict[agent_key].tools
        if self_tools:
            existing_tool_names = [t.name for t in agent_tools]
            for _tool in self_tools:
                # 检查是否已经有同名的tool
                if _tool.name not in existing_tool_names:
                    agent_tools.append(_tool)

        agents_dict[agent_key].handoffs = agent_handoffs
        agents_dict[agent_key].tools = agent_tools


def create_dynamic_instructions(
        system_prompt: str,
) -> Callable[[RunContextWrapper, Agent], Awaitable[str]]:
    async def dynamic_instructions(context: RunContextWrapper, agent: Agent) -> str:
        ctx: AgentExecutionContext = context.context
        variables = ctx.variables
        return await dynamic_prompt(system_prompt, variables)

    return dynamic_instructions


async def create_agent_from_meta(agent_meta: AgentMeta, agent_ctx: AgentContext) -> Agent:
    start_time = time.time()
    agent_props = agent_meta.props

    llm_model = agent_props.model
    model_publisher = llm_model.model_publisher
    model_name = llm_model.name
    model_settings = llm_model.setting

    temperature = model_settings.temperature
    top_p = model_settings.top_p
    max_tokens = model_settings.max_tokens
    frequency_penalty = model_settings.frequency_penalty
    presence_penalty = model_settings.presence_penalty

    # openai_model
    openai_model = OpenAIChatCompletionsModel(
        model=model_name,
        openai_client=await ai_proxy_client_factory.get_client(model_publisher),
    )

    # 设置是否思考
    extra_body = None
    thinking_type = model_settings.get_thinking_type()
    if thinking_type:
        extra_body = {
            "thinking": {
                "type": thinking_type,
            }
        }

    # model_settings
    model_settings = ModelSettings(
        include_usage=True,
        temperature=temperature,
        top_p=top_p,
        max_tokens=max_tokens,
        frequency_penalty=frequency_penalty,
        presence_penalty=presence_penalty,
        extra_body=extra_body,
    )

    system_prompt = agent_props.system_prompt
    # 把关联的模型添加到提示词中
    if agent_meta.props.related_models is not None:
        system_prompt += "\n涉及到的数据模型有：\n"
        for model in agent_meta.props.related_models:
            system_prompt += f"- {model.model_name}: {model.model_key}\n"

    # 如果设置了语言，则添加到提示词中，如果当前agent没有设置，但是主agent设置了语言，则使用主agent的语言
    if (
            (agent_meta.props.reply_with_user_language and agent_meta.props.reply_with_user_language == True) or
            (agent_ctx.main_agent_lang_switch and agent_ctx.main_agent_lang_switch == True)
    ):
        system_prompt += "\n请根据设置的语言来回复用户，当前语言是：${SYS.CurrentTrantorLang}"

    if "${" in agent_props.system_prompt:
        # 说明是动态的prompt
        instructions = create_dynamic_instructions(system_prompt)
    else:
        # 说明是静态的prompt
        instructions = system_prompt

    agent = Agent(
        name=agent_meta.name,
        instructions=instructions,
        model=openai_model,
        model_settings=model_settings,
    )

    # 创建工具
    tools, mcp_servers = await handle_tools(agent_meta, agent_ctx)

    agent.tools = tools
    agent.mcp_servers = mcp_servers

    # 只有在有工具时才设置 parallel_tool_calls
    if tools:
        agent.model_settings.parallel_tool_calls = False  # 默认不并发执行工具

    # 创建hooks
    agent.hooks = CustomAgentHooks()

    # 如果存在最终输出的工具，则设置工具使用行为
    if agent_ctx.any_tool_set_final_output():
        agent.tool_use_behavior = create_tool_use_behavior()

    # 注册Agent信息到上下文中
    agent_ctx.append_agent(agent_meta)

    logger.debug(
        f"========== create agent {agent_meta.key} cost: {round(time.time() - start_time, 2)}秒",
    )
    return agent


def create_tool_use_behavior() -> ToolsToFinalOutputFunction:
    async def tool_use_behavior(
            context: RunContextWrapper[AgentExecutionContext],
            tool_results: list[FunctionToolResult],
    ) -> ToolsToFinalOutputResult:
        agent_exec_content: AgentExecutionContext = context.context
        for tool_result in tool_results:
            tool_name = tool_result.tool.name
            agent_name = tool_result.run_item.agent.name
            tool_meta = agent_exec_content.find_tool_in_agent(tool_name, agent_name)
            if tool_meta is not None:
                # 只要找到其中一个是最终输出的，则直接输出
                if tool_meta.final_output:
                    if isinstance(context, ToolContext):
                        # 判断方法执行是不是失败，如果失败，则不返回最终输出
                        too_mapping = agent_exec_content.tools_call_mapping.get(context.tool_call_id)
                        if too_mapping is not None and too_mapping.is_successful() is not True:
                            return ToolsToFinalOutputResult(is_final_output=False, final_output=None)
                    agent_exec_content.tools_to_final_output_result = True
                    return ToolsToFinalOutputResult(is_final_output=True, final_output=tool_result.output)
        return ToolsToFinalOutputResult(is_final_output=False, final_output=None)

    return tool_use_behavior
