"""
Python代码校验工具
直接复用 PythonSandbox 的校验功能，提供简单的接口
"""
from typing import List, Optional, Tuple
from loguru import logger

from ..sandbox.py_sandbox import PythonSandbox


def validate_python_code_simple(
    code: str, 
    allowed_modules: Optional[List[str]] = None
) -> Tuple[bool, str]:
    """
    简单的Python代码校验接口
    
    直接复用 PythonSandbox._is_safe_code() 方法
    
    Args:
        code: 要校验的Python代码字符串
        allowed_modules: 允许导入的模块列表，如果为None则使用默认列表
        
    Returns:
        Tuple[bool, str]: (是否通过校验, 错误信息)
    """
    try:
        logger.debug(f"开始校验Python代码，代码长度: {len(code)} 字符")
        
        # 创建沙箱实例
        sandbox = PythonSandbox(allowed_modules=allowed_modules)
        
        # 使用沙箱的安全检查方法
        is_safe, error_message = sandbox._is_safe_code(code)
        
        if is_safe:
            logger.debug("Python代码校验通过")
        else:
            logger.warning(f"Python代码校验失败: {error_message}")
        
        return is_safe, error_message
        
    except Exception as e:
        error_msg = f"代码校验异常: {str(e)}"
        logger.error(error_msg)
        return False, error_msg


def validate_code_from_cache(cache_key: str) -> Tuple[bool, str, Optional[str]]:
    """
    从缓存中获取代码并进行校验
    
    Args:
        cache_key: Redis缓存键
        
    Returns:
        Tuple[bool, str, Optional[str]]: (是否通过校验, 错误信息, 解码后的代码)
    """
    try:
        # 延迟导入避免循环依赖
        from .proprietary_tool_cache import proprietary_tool_cache
        
        logger.info(f"从缓存获取代码进行校验: {cache_key}")
        
        # 从Redis获取工具定义
        tool_definition = proprietary_tool_cache.get_tool_definition(cache_key)
        if tool_definition is None:
            error_msg = f"未找到缓存的工具定义: {cache_key}"
            logger.error(error_msg)
            return False, error_msg, None
        
        # 解码Python代码
        decoded_code = proprietary_tool_cache.get_decoded_code(tool_definition)
        if decoded_code is None:
            error_msg = f"无法解码Python代码: {cache_key}"
            logger.error(error_msg)
            return False, error_msg, None
        
        logger.debug(f"成功解码代码，长度: {len(decoded_code)} 字符")
        
        # 校验代码
        is_valid, error_message = validate_python_code_simple(decoded_code)
        
        return is_valid, error_message, decoded_code
        
    except Exception as e:
        error_msg = f"从缓存校验代码异常: {str(e)}"
        logger.error(error_msg)
        return False, error_msg, None


def get_default_allowed_modules() -> List[str]:
    """
    获取默认允许的模块列表
    
    Returns:
        List[str]: 默认允许的模块列表
    """
    return [
        # 标准库 - 数据处理
        'json', 'csv', 'xml', 'html',
        
        # 标准库 - 时间和日期
        'datetime', 'time', 'calendar',
        
        # 标准库 - 数学和随机
        'math', 'random', 'statistics', 'decimal', 'fractions',
        
        # 标准库 - 字符串和文本
        'string', 'textwrap', 're', 'unicodedata',
        
        # 标准库 - 数据结构
        'collections', 'heapq', 'bisect', 'array',
        
        # 标准库 - 函数工具
        'itertools', 'functools', 'operator',
        
        # 标准库 - 类型和数据类
        'typing', 'dataclasses', 'enum',
        
        # 标准库 - 编码和哈希
        'base64', 'hashlib', 'hmac', 'uuid',
        
        # 标准库 - URL和HTTP（受限）
        'urllib', 'http',
        
        # 常用第三方库（需要谨慎）
        # 'requests',  # HTTP请求 - 可能需要网络访问
        # 'numpy',     # 数值计算 - 通常安全
        # 'pandas',    # 数据分析 - 通常安全
    ]


def create_custom_validator(allowed_modules: List[str]):
    """
    创建自定义的代码校验器
    
    Args:
        allowed_modules: 自定义的允许模块列表
        
    Returns:
        function: 校验函数
    """
    def validator(code: str) -> Tuple[bool, str]:
        return validate_python_code_simple(code, allowed_modules)
    
    return validator


# 便捷的预定义校验器
strict_validator = create_custom_validator([
    'json', 'datetime', 'time', 'math', 'random', 'uuid', 'base64'
])

standard_validator = create_custom_validator(get_default_allowed_modules())

permissive_validator = create_custom_validator(
    get_default_allowed_modules() + ['requests', 'numpy', 'pandas']
)
