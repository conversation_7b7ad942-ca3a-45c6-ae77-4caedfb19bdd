import hashlib
import json
import re
import time
import uuid
from collections import OrderedDict
from typing import Any, Dict, List, Optional, Tuple

from loguru import logger
from t_ai_agent.model.agent_dsl import Agent<PERSON><PERSON>
from t_ai_agent.trantor_meta.meta_data import fetch_model_meta
from t_ai_agent.trantor_meta.model_field_helper import get_model_keys
from t_ai_agent.utils.json_util import safe_json_loads
from t_ai_agent.utils.params_mapping_helper import need_to_expand
from t_ai_common.utils.common import is_sys_service, get_service_alias_key


class LRUCache:
    """
    LRU Cache 实现
    """

    def __init__(self, capacity: int):
        self.cache = OrderedDict()
        self.capacity = capacity

    def get(self, key: str) -> Optional[Tuple[str, str, str, str]]:
        if key not in self.cache:
            return None
        # 移动到末尾（最近使用）
        self.cache.move_to_end(key)
        return self.cache[key]

    def put(self, key: str, value: Tuple[str, str, str, str]) -> None:
        if key in self.cache:
            # 如果已存在，移动到末尾
            self.cache.move_to_end(key)
        else:
            # 如果达到容量，删除最久未使用的项
            if len(self.cache) >= self.capacity:
                self.cache.popitem(last=False)
        self.cache[key] = value


# 全局 LRU 缓存实例
_tool_call_cache = LRUCache(200)


def _is_timestamp(value: Any) -> bool:
    """
    检查值是否为时间戳

    Args:
        value: 要检查的值

    Returns:
        bool: 是否为时间戳
    """
    if not isinstance(value, (int, float)):
        return False

    # 检查是否在合理的时间戳范围内（2000-01-01 到 2100-01-01）
    try:
        timestamp = float(value)
        return 946684800 <= timestamp <= 4102444800
    except:
        return False


def _is_datetime_string(value: str) -> bool:
    """
    检查字符串是否为日期时间格式

    Args:
        value: 要检查的字符串

    Returns:
        bool: 是否为日期时间格式
    """
    if not isinstance(value, str):
        return False

    # 常见的日期时间格式模式
    patterns = [
        r"\d{4}-\d{2}-\d{2}",  # YYYY-MM-DD
        r"\d{4}/\d{2}/\d{2}",  # YYYY/MM/DD
        r"\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}",  # YYYY-MM-DD HH:mm:ss
        r"\d{4}/\d{2}/\d{2} \d{2}:\d{2}:\d{2}",  # YYYY/MM/DD HH:mm:ss
        r"\d{2}:\d{2}:\d{2}",  # HH:mm:ss
        r"\d{2}:\d{2}",  # HH:mm
    ]

    return any(re.match(pattern, value) for pattern in patterns)


def _has_time_value(params_str: str) -> bool:
    """
    检查参数中是否包含时间值

    Args:
        params_str: JSON格式的参数字符串

    Returns:
        bool: 是否包含时间值
    """
    try:
        params = json.loads(params_str)

        def check_value(value: Any) -> bool:
            if isinstance(value, dict):
                return any(check_value(v) for v in value.values())
            elif isinstance(value, (list, tuple)):
                return any(check_value(item) for item in value)
            elif isinstance(value, str):
                return _is_datetime_string(value)
            else:
                return _is_timestamp(value)

        return check_value(params)
    except:
        return False


def _get_field_type_from_meta(field_key: str, model_meta: Dict[str, Any]) -> Optional[str]:
    """
    从模型元数据中获取字段类型

    Args:
        field_key: 字段key
        model_meta: 模型元数据

    Returns:
        Optional[str]: 字段类型，如果找不到则返回None
    """
    if not model_meta or "data" not in model_meta:
        return None

    for model in model_meta["data"]:
        fields = model.get("fields", [])
        for field in fields:
            if field.get("alias") == field_key:
                return field.get("fieldType")
    return None


def _should_skip_cache_for_time_field(params_str: str, input_meta: list, model_meta: Dict[str, Any]) -> bool:
    """
    检查是否应该跳过缓存（仅当时间类型字段有值时）

    Args:
        params_str: JSON格式的参数字符串
        input_meta: 输入字段元数据
        model_meta: 模型元数据

    Returns:
        bool: 是否应该跳过缓存
    """
    try:
        params = json.loads(params_str)

        # 遍历参数中的所有字段
        for key, value in params.items():
            # 只有当字段有值时才进行检查
            if value is not None:
                # 获取字段类型
                field_type = _get_field_type_from_meta(key, model_meta)
                # 只有当字段类型为时间类型时才跳过缓存
                if field_type in ["Time", "DateTime"]:
                    return True

        return False
    except Exception as e:
        logger.warning(f"Failed to check time field in params: {e}")
        return False


def _calculate_cache_key(input_items: list, agent_meta: AgentMeta) -> str:
    """
    计算缓存key

    Args:
        input_items: 用户输入列表
        agent_meta: Agent元数据

    Returns:
        str: 缓存key的md5值
    """
    # 将input_items和agent_meta转换为JSON字符串
    cache_str = json.dumps({"input_items": input_items, "agent_meta": agent_meta.model_dump_json()}, sort_keys=True)

    # 计算md5
    cache_key = hashlib.md5(cache_str.encode()).hexdigest()
    # logger.debug(f"################ cache_key: {cache_key}, cache_str: {cache_str}")
    return cache_key


def get_cached_tool_call(input_items: list, agent_meta: AgentMeta) -> Optional[Tuple[str, str, str]]:
    """
    获取缓存的工具调用

    Args:
        input_items: 用户输入列表
        agent_meta: Agent元数据

    Returns:
        Optional[Tuple[str, str, str]]: 如果命中缓存返回(service_key, params_str, service_name)，否则返回None
    """
    cache_key = _calculate_cache_key(input_items, agent_meta)
    cached_result = _tool_call_cache.get(cache_key)
    if cached_result:
        logger.debug(f"Tool call cache hit for key: {cache_key}")
    return cached_result


def cache_tool_call(
    input_items: list,
    origin_agent_meta: AgentMeta,
    current_agent_meta: AgentMeta,
    service_key: str,
    params_str: str,
    invoke_status: str = "success",
) -> None:
    """
    缓存工具调用

    Args:
        input_items: 用户输入列表
        origin_agent_meta: Agent元数据
        current_agent_meta: Agent元数据
        service_key: 服务Key
        params_str: 参数JSON字符串
        invoke_status: 工具调用状态，默认为"success"
    """
    # 如果工具调用失败，不进行缓存
    if invoke_status != "success":
        logger.debug(f"Tool {service_key} call failed with status {invoke_status}, skipping cache")
        return

    # 获取工具配置
    skill_tools = current_agent_meta.props.skill_tools or []
    target_tool = None
    target_tool_key = None
    # logger.debug(f"################ current_agent_meta: {current_agent_meta}")
    logger.debug(f"################ service_key: {service_key}, skill_tools: {skill_tools}")

    if skill_tools is None:
        return

    for tool in skill_tools:
        if tool.key.endswith(service_key):
            target_tool = tool
            target_tool_key = tool.key
            break

    if not target_tool:
        logger.debug(f"Tool {service_key} not found in skill tools, skipping cache")
        return

    service_name = target_tool.name or f"{target_tool_key}"

    if target_tool.type != "service":
        logger.debug(f"Tool {target_tool_key} is not a service tool, skipping cache")
        return

    if not target_tool.input:
        logger.debug(f"Tool {target_tool_key} has no input fields, proceeding with cache")
        cache_key = _calculate_cache_key(input_items, origin_agent_meta)
        _tool_call_cache.put(cache_key, (target_tool_key, params_str, service_name, current_agent_meta.name))
        return

    input_fields = target_tool.input or []

    # 只有当需要扩展时才获取模型元数据
    if need_to_expand(input_fields):
        # 获取模型key
        model_keys = get_model_keys(input_fields)
        if not model_keys:
            logger.debug(f"Tool {target_tool_key} has no model keys, proceeding with cache")
            cache_key = _calculate_cache_key(input_items, origin_agent_meta)
            _tool_call_cache.put(cache_key, (target_tool_key, params_str, service_name, current_agent_meta.name))
            return

        # 获取模型元数据
        model_meta = fetch_model_meta(model_keys)
        if model_meta:
            # 检查参数中的时间字段是否有值
            if _should_skip_cache_for_time_field(params_str, input_fields, model_meta):
                logger.debug(f"Tool {target_tool_key} has time field with value in params, skipping cache")
                return

    # logger.debug(f"################ cache_tool_call: {target_tool_key} {params_str}")

    cache_key = _calculate_cache_key(input_items, origin_agent_meta)
    _tool_call_cache.put(cache_key, (target_tool_key, params_str, service_name, current_agent_meta.name))


def match_tool_call_cache(input_items: list, agent_meta: AgentMeta) -> Optional[Tuple[str, str, str, str, str]]:
    """
    检查并匹配缓存的工具调用

    Args:
        input_items: 用户输入列表
        agent_meta: Agent元数据

    Returns:
        Optional[Tuple[str, str, str]]: 如果命中缓存返回(final_service_key, params_str, service_name)，否则返回None
    """
    cache_key = _calculate_cache_key(input_items, agent_meta)
    cached_result = _tool_call_cache.get(cache_key)
    if not cached_result:
        return None

    service_key, params_str, service_name, agent_name = cached_result
    logger.info(f"################ cached_tool_call: {cached_result}")

    # 处理系统服务的情况
    final_service_key = service_key
    if is_sys_service(service_key):
        try:
            params = safe_json_loads(params_str)
            model_key = params.get("modelKey")
            if model_key:
                final_service_key = final_service_key + "?modelKey=" + model_key
        except Exception as e:
            logger.warning(f"Failed to process sys service params: {e}")
            return None

    return final_service_key, params_str, service_name, service_key, agent_name


def create_cached_tool_call_messages(
    agent_meta: AgentMeta, service_key: str, params_str: str, service_name: str, result: Dict[str, Any]
) -> Tuple[List[Dict[str, Any]], List[Dict[str, Any]]]:
    """
    创建缓存工具调用的消息列表和输入项

    Returns:
        Tuple[List[Dict[str, Any]], List[Dict[str, Any]]]: (input_items, messages)
            - input_items: 包含函数调用和输出的输入项列表
            - messages: SSE 消息列表
    """
    meta = {"key": agent_meta.key, "name": agent_meta.name, "avatar": agent_meta.props.avatar}
    conversation_id = "cached_conversation"
    parent_id = "cached_parent"
    created_at = int(time.time() * 1000)
    call_id = f"cached_call_{int(time.time())}"

    # 创建输入项
    input_items = [
        {"call_id": call_id, "name": get_service_alias_key(service_key), "arguments": params_str, "type": "function_call"},
        {"call_id": call_id, "output": json.dumps(result, ensure_ascii=False), "type": "function_call_output"},
    ]

    # 1. 创建 function_call 消息
    function_call_msg = {
        "meta": meta,
        "message_id": str(uuid.uuid4()),
        "parent_id": parent_id,
        "conversation_id": conversation_id,
        "created_at": created_at,
        "role": "assistant",
        "content": {
            "type": "function_call",
            "status": "DOING",
            "call_id": call_id,
            "key": service_key,
            "name": service_name,
            "arguments": params_str,
        },
    }

    # 2. 创建 function_call_arguments 消息
    arguments_msg = {
        "meta": meta,
        "message_id": str(uuid.uuid4()),
        "parent_id": parent_id,
        "conversation_id": conversation_id,
        "created_at": created_at,
        "role": "assistant",
        "content": {"type": "function_call_arguments", "status": "DONE", "call_id": call_id, "arguments": params_str},
    }

    # 3. 创建 function_call_output 消息
    output_msg = {
        "meta": meta,
        "message_id": str(uuid.uuid4()),
        "parent_id": parent_id,
        "conversation_id": conversation_id,
        "created_at": created_at,
        "role": "assistant",
        "content": {
            "type": "function_call_output",
            "status": "DONE",
            "call_id": call_id,
            "output": json.dumps(result, ensure_ascii=False),
            "invoke_cost_time": 0.1,
            "invoke_status": "success",
        },
    }

    return input_items, [function_call_msg, arguments_msg, output_msg]
