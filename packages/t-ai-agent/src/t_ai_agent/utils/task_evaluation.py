import json
import time
import uuid
from dataclasses import dataclass
from typing import Optional

from agents import Agent, OpenAIChatCompletionsModel, Runner
from loguru import logger
from t_ai_agent.ai_proxy_client_factory import ai_proxy_client_factory
from t_ai_agent.model.agent_dsl import Agent<PERSON><PERSON>
from t_ai_agent.model.agent_message import AgentMessage, AgentMessageMeta, AgentTextContent, TaskStartContent


@dataclass
class TaskEvaluationFeedback:
    feedback_to_user: str
    task_name: str
    new_input: str
    result: bool


@dataclass
class TaskEvaluationResult(TaskEvaluationFeedback):
    agent_meta: AgentMeta
    task_id: Optional[str] = None

    def get_task_start_message(self):
        if not self.task_id:
            self.task_id = str(uuid.uuid4())
        meta = AgentMessageMeta.from_agent_meta(self.agent_meta)
        return AgentMessage(
            content=TaskStartContent(task_id=self.task_id, task_name=self.task_name, new_input=self.new_input,
                                     arguments=self.new_input),
            meta=meta,
        )

    def get_task_feedback_message(self):
        meta = AgentMessageMeta.from_agent_meta(self.agent_meta)
        return AgentMessage(
            content=AgentTextContent(text=self.feedback_to_user, status="DONE"),
            meta=meta,
        )


class TaskEvaluation:

    @classmethod
    async def evaluate_task_complexity(
            cls, list_input: list, agent_meta: AgentMeta
    ) -> TaskEvaluationResult:
        """
        评估用户是否要求异步执行任务

        Args:
            list_input: 输入消息列表，包含用户的请求和上下文信息
            agent_meta: Agent 元数据，包含模型信息和其他配置

        Returns:
            EvaluationFeedback: 评估反馈
        """

        # 获取最后一个用户输入
        last_user_text = None
        for item in reversed(list_input):
            # TResponseInputItem 是一个 TypedDict，使用字典访问语法
            if isinstance(item, dict) and item.get("role") == "user":
                content_list = json.loads(item.get("content", ""))
                # 只关注文本内容
                text_content = next(
                    (
                        content_item
                        for content_item in content_list
                        if isinstance(content_item, dict) and content_item.get("type") == "text"
                    ),
                    None,
                )
                if text_content and isinstance(text_content, dict):
                    last_user_text = text_content.get("text", "")
                    break

        if not last_user_text:
            logger.warning("No valid user input text found in the conversation history")
            return TaskEvaluationResult(
                result=False, task_name="", new_input="", feedback_to_user="", agent_meta=agent_meta
            )

        # last_user_text 如果少于4个字段，则直接返回
        if len(last_user_text) < 4:
            return TaskEvaluationResult(
                result=False, task_name="", new_input="", feedback_to_user="", agent_meta=agent_meta
            )

        return await cls.evaluate_task_complexity_by_keywords(last_user_text, agent_meta)

    @classmethod
    async def evaluate_task_complexity_by_agent(cls, user_text: str, agent_meta: AgentMeta) -> TaskEvaluationResult:
        """
        使用评估器Agent来评估任务是否需要异步执行
        """
        start_time = time.time()
        try:
            evaluator_agent = await cls.create_evaluator_agent(
                # agent_meta.props.model.model_publisher, agent_meta.props.model.name
                "openai",
                "gpt-4o-mini",  # 使用默认的模型和发布者
            )

            # 构造简化的输入项，只包含最后一条用户消息
            simplified_input = "请评估以下用户问题是否需要异步执行。用户的问题是：" + user_text

            # 运行评估，只使用最后一个用户输入的文本内容
            evaluator_result = await Runner.run(starting_agent=evaluator_agent, input=simplified_input, max_turns=1)

            result: TaskEvaluationFeedback = evaluator_result.final_output
            logger.debug(f"################ evaluation result: {result} ")
            logger.debug(f"################ evaluation cost: {round(time.time() - start_time, 2)}")
            return TaskEvaluationResult(
                result=result.result,
                task_name=result.task_name,
                new_input=result.new_input,
                feedback_to_user=result.feedback_to_user,
                agent_meta=agent_meta,
            )
        except Exception as e:
            logger.exception(f"Error evaluating task complexity: {str(e)}")
            return TaskEvaluationResult(
                result=False, task_name="", new_input="", feedback_to_user="", agent_meta=agent_meta
            )

    @classmethod
    async def evaluate_task_complexity_by_keywords(cls, user_text: str, agent_meta: AgentMeta) -> TaskEvaluationResult:
        """
        使用关键词来评估任务是否需要异步执行
        """
        key_words = ["异步执行", "后台执行"]
        # 检查用户输入是否包含任何关键词
        if any(keyword in user_text for keyword in key_words):
            # 如果用户明确要求异步执行，则直接返回评估结果
            result = await cls.evaluate_task_complexity_by_agent(user_text, agent_meta)
            result.result = True
            if result.task_name and len(result.task_name) <= 0:
                result.task_name = "异步任务"
            if result.feedback_to_user and len(result.feedback_to_user) <= 0:
                result.feedback_to_user = f"任务已在后台启动，预计需要一些时间，完成后会通知您"
            return result
        else:
            return TaskEvaluationResult(
                result=False, task_name="", new_input="", feedback_to_user="", agent_meta=agent_meta
            )

    @classmethod
    async def create_evaluator_agent(cls, model_publisher, model_name) -> Agent:
        # 你是一个任务评估专家。你需要综合分析用户的意图和任务的性质，判断是否需要异步执行。

        # 请从以下几个维度进行分析：

        # 1. 用户意图分析：
        #    - 用户是否明确表示希望任务在后台运行
        #    - 用户是否暗示任务应该异步处理（如"帮我处理"、"帮我准备"等）
        #    - 用户的表述是否暗含不希望等待（如"这个可能要很久"）

        # 2. 任务复杂度分析：
        #    - 任务是否涉及大量数据处理或计算
        #    - 任务是否需要多轮操作或复杂的工作流
        #    - 任务是否可能需要较长时间完成（如文件处理、数据分析）

        # 3. 任务类型分析：
        #    - 数据分析和统计类任务
        #    - 批量处理或转换类任务
        #    - 需要外部资源或服务的任务
        #    - 可能耗时的文档处理任务

        # 请仔细理解用户的真实意图和任务本质，不要仅依赖关键词。
        # 返回 EvaluationFeedback 对象：

        # - task_name: 用简短的词组描述任务（如"数据分析"、"文件处理"、"批量转换"等）
        # - result: 布尔值
        #   - true = 需要异步执行（满足以下任一条件）：
        #     1. 用户明确要求异步
        #     2. 任务预计耗时较长（>30秒）
        #     3. 任务涉及复杂处理流程
        #     4. 任务类型适合异步处理
        #   - false = 同步执行（简单、快速的任务）

        # - feedback_to_user: 根据不同情况返回合适的反馈：
        #   1. 异步任务反馈模板：
        #      - 数据分析类："正在后台进行数据分析，完成后会通知您查看结果"
        #      - 文件处理类："文件正在后台处理中，处理完成后会通知您"
        #      - 批量操作类："批量处理任务已启动，完成后会通知您"
        #      - 其他复杂任务："任务已在后台启动，预计需要一些时间，完成后会通知您"

        #   2. 同步任务反馈：
        #      "任务正在执行中，请稍候..."

        # 注意：如果任务描述不够清晰或无法判断复杂度，优先选择同步执行。

        evaluator = Agent(
            name="task_evaluator",
            model=OpenAIChatCompletionsModel(
                model=model_name,
                openai_client=await ai_proxy_client_factory.get_client(model_publisher),
            ),
            output_type=TaskEvaluationFeedback,
            instructions="""你是一个任务评估专家。你需要综合分析用户的意图和任务的性质，判断是否需要异步执行。

    请根据用户意图分析：
    用户意图分析：
    - 用户是否明确表示希望任务在后台运行
    - 用户是否暗示任务应该异步处理（如"异步处理"、"在后台执行"等）
    - 用户的表述是否暗含不希望等待（如"这个可能要很久"）

    请仔细理解用户的真实意图，不要仅依赖关键词。
    返回 EvaluationFeedback 对象：
    - task_name: 用简短的词组描述任务（如"数据分析"、"文件处理"、"批量转换"等）
    - result: 布尔值
        - true = 需要异步执行
        - false = 同步执行（简单、快速的任务）
    - new_input: 把用户的输入转换成同步执行的输入
    - feedback_to_user: 根据不同情况返回合适的反馈：
        1. 异步任务反馈模板：
            - 数据分析类："正在后台进行数据分析，完成后会通知您查看结果"
            - 文件处理类："文件正在后台处理中，处理完成后会通知您"
            - 批量操作类："批量处理任务已启动，完成后会通知您"
            - 其他复杂任务："任务已在后台启动，预计需要一些时间，完成后会通知您"
        2. 同步任务反馈：
            "任务正在执行中，请稍候..."

    注意：如果任务描述不够清晰或无法判断复杂度，优先选择同步执行。""",
        )
        return evaluator
