import base64
import json
from typing import Optional

from loguru import logger

from t_ai_common.clients.redis_client import get_redis_client, RedisSettings
from ..model.proprietary_tool import ProprietaryToolDefinition


class ProprietaryToolCache:
    """专有工具缓存服务"""

    def __init__(self, redis_settings: Optional[RedisSettings] = None):
        self.redis_client = get_redis_client(redis_settings)
    
    def get_tool_definition(self, cache_key: str) -> Optional[ProprietaryToolDefinition]:
        """
        从Redis获取专有工具定义
        
        Args:
            cache_key: 缓存键
            
        Returns:
            ProprietaryToolDefinition: 专有工具定义对象，如果不存在则返回None
        """
        try:
            logger.debug(f"从Redis获取专有工具定义，cache_key: {cache_key}")
            
            # 从Redis获取缓存数据
            cached_data = self.redis_client.get_json(cache_key)
            if cached_data is None:
                logger.warning(f"Redis中未找到专有工具定义，cache_key: {cache_key}")
                return None
            
            logger.debug(f"从Redis获取到缓存数据: {cached_data}")
            
            # 转换为ProprietaryToolDefinition对象
            tool_definition = self._convert_to_tool_definition(cached_data)
            
            if tool_definition is None:
                logger.error(f"转换专有工具定义失败，cache_key: {cache_key}")
                return None
            
            logger.info(f"成功从Redis获取并转换专有工具定义，cache_key: {cache_key}")
            return tool_definition
            
        except Exception as e:
            logger.error(f"从Redis获取专有工具定义异常，cache_key: {cache_key}, 错误: {e}")
            return None
    
    def set_tool_definition(self, cache_key: str, tool_definition: ProprietaryToolDefinition, ex: Optional[int] = None) -> bool:
        """
        将专有工具定义存储到Redis
        
        Args:
            cache_key: 缓存键
            tool_definition: 专有工具定义对象
            ex: 过期时间（秒）
            
        Returns:
            bool: 是否存储成功
        """
        try:
            logger.debug(f"将专有工具定义存储到Redis，cache_key: {cache_key}")
            
            # 转换为字典格式
            tool_data = self._convert_from_tool_definition(tool_definition)
            
            # 存储到Redis
            success = self.redis_client.set_json(cache_key, tool_data, ex=ex)
            
            if success:
                logger.info(f"成功将专有工具定义存储到Redis，cache_key: {cache_key}")
            else:
                logger.error(f"存储专有工具定义到Redis失败，cache_key: {cache_key}")
            
            return success
            
        except Exception as e:
            logger.error(f"存储专有工具定义到Redis异常，cache_key: {cache_key}, 错误: {e}")
            return False
    
    def _convert_to_tool_definition(self, cached_data: dict) -> Optional[ProprietaryToolDefinition]:
        """
        将缓存数据转换为ProprietaryToolDefinition对象
        
        Args:
            cached_data: 从Redis获取的缓存数据
            
        Returns:
            ProprietaryToolDefinition: 转换后的对象
        """
        try:
            # 创建ProprietaryToolDefinition对象
            tool_definition = ProprietaryToolDefinition()
            
            # 设置基本属性
            tool_definition.description = cached_data.get("description")
            
            # 处理implementation字段
            implementation_data = cached_data.get("implementation")
            if implementation_data:
                implementation = ProprietaryToolDefinition.Implementation()
                implementation.code = implementation_data.get("code")
                implementation.encoding = implementation_data.get("encoding", "base64")
                implementation.language = implementation_data.get("language", "python")
                tool_definition.implementation = implementation
            
            # 处理toolSchema字段
            tool_schema_data = cached_data.get("toolSchema")
            if tool_schema_data:
                tool_schema = ProprietaryToolDefinition.ToolSchema()
                tool_schema.properties = tool_schema_data.get("properties")
                tool_schema.required = tool_schema_data.get("required")
                tool_schema.type = tool_schema_data.get("type", "object")
                tool_definition.tool_schema = tool_schema
            
            return tool_definition
            
        except Exception as e:
            logger.error(f"转换缓存数据为ProprietaryToolDefinition异常: {e}")
            return None
    
    def _convert_from_tool_definition(self, tool_definition: ProprietaryToolDefinition) -> dict:
        """
        将ProprietaryToolDefinition对象转换为字典格式
        
        Args:
            tool_definition: 专有工具定义对象
            
        Returns:
            dict: 转换后的字典
        """
        result = {}
        
        if tool_definition.description is not None:
            result["description"] = tool_definition.description
        
        if tool_definition.implementation is not None:
            implementation_data = {}
            if tool_definition.implementation.code is not None:
                implementation_data["code"] = tool_definition.implementation.code
            if tool_definition.implementation.encoding is not None:
                implementation_data["encoding"] = tool_definition.implementation.encoding
            if tool_definition.implementation.language is not None:
                implementation_data["language"] = tool_definition.implementation.language
            result["implementation"] = implementation_data
        
        if tool_definition.tool_schema is not None:
            tool_schema_data = {}
            if tool_definition.tool_schema.properties is not None:
                tool_schema_data["properties"] = tool_definition.tool_schema.properties
            if tool_definition.tool_schema.required is not None:
                tool_schema_data["required"] = tool_definition.tool_schema.required
            if tool_definition.tool_schema.type is not None:
                tool_schema_data["type"] = tool_definition.tool_schema.type
            result["toolSchema"] = tool_schema_data
        
        return result


    def get_decoded_code(self, tool_definition: ProprietaryToolDefinition) -> Optional[str]:
        """
        获取解码后的Python代码

        Args:
            tool_definition: 专有工具定义对象

        Returns:
            str: 解码后的Python代码，如果解码失败则返回None
        """
        try:
            if (tool_definition.implementation is None or
                tool_definition.implementation.code is None):
                logger.warning("专有工具定义中没有代码实现")
                return None

            code = tool_definition.implementation.code
            encoding = tool_definition.implementation.encoding or "base64"

            if encoding.lower() == "base64":
                # Base64解码
                decoded_bytes = base64.b64decode(code)
                decoded_code = decoded_bytes.decode('utf-8')
                logger.debug("成功解码Base64编码的Python代码")
                return decoded_code
            else:
                # 其他编码或纯文本
                logger.debug(f"使用{encoding}编码的代码，直接返回")
                return code

        except Exception as e:
            logger.error(f"解码Python代码失败: {e}")
            return None


# 全局实例
proprietary_tool_cache = ProprietaryToolCache()
