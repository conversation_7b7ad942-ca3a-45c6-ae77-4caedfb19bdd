"""
专有工具辅助函数
"""
import base64
from typing import Optional

from loguru import logger

from .proprietary_tool_cache import proprietary_tool_cache
from ..model.proprietary_tool import ProprietaryToolDefinition


def create_sample_tool_definition(python_code: str, description: str = "示例专有工具") -> ProprietaryToolDefinition:
    """
    创建示例专有工具定义
    
    Args:
        python_code: Python代码
        description: 工具描述
        
    Returns:
        ProprietaryToolDefinition: 专有工具定义对象
    """
    # 编码Python代码
    encoded_code = base64.b64encode(python_code.encode('utf-8')).decode('ascii')
    
    # 创建工具定义
    tool_definition = ProprietaryToolDefinition()
    tool_definition.description = description
    
    # 创建实现部分
    implementation = ProprietaryToolDefinition.Implementation()
    implementation.code = encoded_code
    implementation.encoding = "base64"
    implementation.language = "python"
    tool_definition.implementation = implementation
    
    # 创建工具Schema
    tool_schema = ProprietaryToolDefinition.ToolSchema()
    tool_schema.type = "object"
    tool_schema.properties = {
        "message": {
            "type": "string",
            "description": "输入消息"
        }
    }
    tool_schema.required = ["message"]
    tool_definition.tool_schema = tool_schema
    
    return tool_definition


def store_sample_tool_to_redis(cache_key: str, python_code: str, description: str = "示例专有工具") -> bool:
    """
    将示例专有工具存储到Redis
    
    Args:
        cache_key: 缓存键
        python_code: Python代码
        description: 工具描述
        
    Returns:
        bool: 是否存储成功
    """
    try:
        tool_definition = create_sample_tool_definition(python_code, description)
        return proprietary_tool_cache.set_tool_definition(cache_key, tool_definition)
    except Exception as e:
        logger.error(f"存储示例专有工具失败: {e}")
        return False


def get_tool_from_redis(cache_key: str) -> Optional[ProprietaryToolDefinition]:
    """
    从Redis获取专有工具定义
    
    Args:
        cache_key: 缓存键
        
    Returns:
        ProprietaryToolDefinition: 专有工具定义对象，如果不存在则返回None
    """
    return proprietary_tool_cache.get_tool_definition(cache_key)


def test_redis_connection() -> bool:
    """
    测试Redis连接
    
    Returns:
        bool: 连接是否正常
    """
    try:
        return proprietary_tool_cache.redis_client.ping()
    except Exception as e:
        logger.error(f"Redis连接测试失败: {e}")
        return False


# 示例Python代码
SAMPLE_PYTHON_CODE = '''
def main(arguments):
    """
    示例专有工具函数
    """
    message = arguments.get("message", "Hello World!")
    
    # 处理消息
    processed_message = f"处理后的消息: {message.upper()}"
    
    # 返回结果
    result = {
        "original_message": message,
        "processed_message": processed_message,
        "status": "success"
    }
    
    return result

# 设置result变量，这是沙箱执行的返回值
if __name__ == "__main__":
    result = main(arguments)
'''
