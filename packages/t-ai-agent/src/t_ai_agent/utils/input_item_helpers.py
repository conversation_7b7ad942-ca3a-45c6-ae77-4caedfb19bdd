import json
from typing import Union

from agents import HandoffInputData
from agents.items import TResponseInputItem, HandoffCallItem, HandoffOutputItem, RunItem, ToolCallItem
from openai.types.responses import ResponseFunctionToolCall
from openai.types.responses.response_input_file_param import ResponseInputFileParam
from openai.types.responses.response_input_image_param import ResponseInputImageParam

from t_ai_common.utils.common import parse_file_name, get_service_alias_key
from .attachment_handler import process_attachments
from ..model import AgentExecutionContext


async def to_input_items(input_items) -> list[TResponseInputItem]:
    """
        将输入的input_items转换为TResponseInputItem,
        因为 input_items 是外部输入，需要进行转换, input_items 的格式如下:
        ```
        [
            {
                "role": "user",
                "content": "[{\"type\":\"text\",\"text\":\"你好\"},{\"type\":\"attachments\",\"attachments\":[...]}]"
            },
            {
                "role": "assistant",
                "content": "{\"type\":\"function_call\",\"key\":\"tool_key\",\"arguments\":{\"param1\":\"value1\",\"param2\":\"value2\"}}"
            },
        ]
        ```
        """
    result = []

    if not input_items:
        return result

    for item in input_items:
        # Handle user or assistant messages
        content = item.get("content", "")

        if item["role"] == "user":
            content_list = json.loads(content)

            # Convert to proper content format
            attachment_infos = await _get_all_attachment_info(content_list)
            attachment_prompt = _get_attachment_prompt(attachment_infos)

            formatted_content = []
            for content_item in content_list:
                if content_item.get("type") == "text":
                    # 处理文本内容
                    text = content_item.get("text", "")

                    # 处理文本内容中的附件URL
                    if len(attachment_prompt) > 0:
                        text = text + "\n" + attachment_prompt

                    formatted_content.append(
                        {
                            "type": "input_text",
                            "text": text,
                        }
                    )
                elif content_item.get("type") == "attachments":
                    # 处理附件
                    attachments = content_item.get("attachments", [])
                    attachment_items = await _to_attachments_items(attachments)
                    for attachment_item in attachment_items:
                        formatted_content.append(attachment_item)

            # 如果 内容为空，则把附件作为内容
            if len(formatted_content) <= 0:
                formatted_content.append({"type": "input_text", "text": attachment_prompt})

            result.append({"role": item["role"], "content": formatted_content, "type": "message"})

        elif item["role"] == "assistant":
            content_json = json.loads(content)
            if content_json.get("type") == "text":
                result.append(
                    {
                        "role": item["role"],
                        "content": [{"type": "output_text", "text": content_json.get("text", "")}],
                        "type": "message",
                    }
                )
            elif content_json.get("type") == "function_call":
                result.append(
                    {
                        "call_id": content_json.get("callId", ""),
                        "name": get_service_alias_key(content_json.get("key", "")),
                        "arguments":'{}', # content_json.get("arguments", "{}"), 这里把参数置成空，1.是为了减少上下文，减少token，2.也是为了较少干扰
                        "type": "function_call",
                    }
                )
            elif content_json.get("type") == "function_call_output":
                invoke_status = content_json.get("invokeStatus", "success")
                result.append(
                    {
                        "call_id": content_json.get("callId", ""),
                        "output": content_json.get("output", "{}"),
                        "type": "function_call_output",
                        "status": "completed" if invoke_status == 'success' else "incomplete",
                    }
                )
        else:
            result.append({"role": item["role"], "content": content, "type": "message"})

    return result


async def _to_attachments_items(attachments) -> list[Union[ResponseInputImageParam, ResponseInputFileParam]]:
    result = []

    if not attachments:
        return result

    attachment_results = await process_attachments(attachments) if attachments else []
    for attachment in attachment_results:
        result.append(attachment.to_agent_input())

    return result


async def _get_all_attachment_info(content_list):
    attachment_info = []
    for content_item in content_list:
        if content_item.get("type") == "attachments":
            attachments = content_item.get("attachments", [])
            # 附件支持2种格式，一种是字典列表，一种是字符串列表
            for attachment in attachments:
                if isinstance(attachment, dict):
                    attachment_url = attachment.get("encodeUrl") or attachment.get("url")
                    file_name = attachment.get("fileName")
                    if attachment_url and file_name:
                        attachment_info.append({"url": attachment_url, "fileName": file_name})
                else:
                    attachment_url = attachment
                    attachment_info.append({"url": attachment_url, "fileName": parse_file_name(attachment_url)})
    return attachment_info


def to_input_items_remove_handoff_tools(
        input_items: list[TResponseInputItem],
        agent_exec_context: AgentExecutionContext
) -> list[TResponseInputItem]:
    result = []
    for item in input_items:
        call_id = item.get("call_id", None)
        if call_id and call_id not in agent_exec_context.tools_call_mapping:
            continue
        result.append(item)
    return result


def _get_attachment_prompt(attachment_infos):
    if not attachment_infos:
        return ""
    attachment_list = [f"- {info['fileName']} (url = {info['url']})" for info in attachment_infos]
    return f"\n附件列表（url 是文件地址，请严格保持原样，禁止修改）：\n" + "\n".join(attachment_list)


def remove_handoff_tools(handoff_input_data: HandoffInputData) -> HandoffInputData:
    """Filters out all tool items: file search, web search and function calls+output."""

    history = handoff_input_data.input_history
    new_items = handoff_input_data.new_items

    filtered_pre_handoff_items = _remove_tools_from_items(handoff_input_data.pre_handoff_items)
    filtered_new_items = _remove_tools_from_items(new_items)

    return HandoffInputData(
        input_history=history,
        pre_handoff_items=filtered_pre_handoff_items,
        new_items=filtered_new_items,
        run_context=handoff_input_data.run_context,
    )


def _remove_tools_from_items(items: tuple[RunItem, ...]) -> tuple[RunItem, ...]:
    filtered_items = []
    for item in items:
        if (
                isinstance(item, HandoffCallItem)
                or isinstance(item, HandoffOutputItem)
        ):
            continue
        if (
                isinstance(item, ToolCallItem)
                and isinstance(item.raw_item, ResponseFunctionToolCall)
        ):
            # 这里把参数置空，1.是为了减少上下文，减少token，2.也是为了较少干扰
            item.raw_item.arguments = '{}'
        filtered_items.append(item)
    return tuple(filtered_items)
