import asyncio
import time
from dataclasses import dataclass
from typing import Optional

from agents import Agent, ModelSettings, OpenAIChatCompletionsModel, TResponseInputItem, Runner
from loguru import logger

from t_ai_agent.ai_proxy_client_factory import ai_proxy_client_factory
from t_ai_agent.model.result_validation_config import ResultValidationConfig
from t_ai_agent.utils.trantor_helper import get_result_validation_config
from t_ai_app.ctx import ReqCtx


@dataclass
class EvaluationFeedback:
    feedback: str
    score: float
    detail: Optional[str]


class EvaluationAgentFactory:
    config_cache: dict[str, ResultValidationConfig] = {}
    locks: dict[str, asyncio.Lock] = {}  # 每个 team_id 对应一把锁
    global_lock = asyncio.Lock()  # 用来保护 locks 字典本身

    @classmethod
    async def get_config(cls) -> Optional[ResultValidationConfig]:
        config = await get_result_validation_config()
        if config is not None:
            return ResultValidationConfig.model_validate(config)
        return None

    @classmethod
    async def create_evaluation_agent(cls, cache_enabled: bool = False) -> 'EvaluationAgent':
        team_id = ReqCtx.get_team_id()
        config = None

        if not cache_enabled:
            config = await cls.get_config()
        else:
            # 先检查缓存
            config = cls.config_cache.get(team_id)
            if config is not None:
                return EvaluationAgent(config)

            # 确保 locks[team_id] 存在
            async with cls.global_lock:
                if team_id not in cls.locks:
                    cls.locks[team_id] = asyncio.Lock()
                lock = cls.locks[team_id]

            # 使用该 team_id 的锁来避免重复加载
            async with lock:
                # 再次检查缓存（可能别的协程已经写入了）
                config = cls.config_cache.get(team_id)
                if config is None:
                    config = await cls.get_config()
                    if config is not None:
                        cls.config_cache[team_id] = config

        if config is not None:
            return EvaluationAgent(config)
        else:
            return EvaluationAgent(ResultValidationConfig(enabled=False))


class EvaluationAgent:
    def __init__(self, config: ResultValidationConfig):
        self._index: int = 0
        self._config: ResultValidationConfig = config
        self._agent: Agent | None = None

    def get_evaluation_count(self):
        return self._index

    def enabled(self):
        return self._config.enabled and self._index < self._config.max_retries

    async def _create_agent(self):
        agent = Agent(
            name="evaluator",
            model_settings=ModelSettings(
                temperature=self._config.model.setting.temperature,
                top_p=self._config.model.setting.top_p,
                frequency_penalty=self._config.model.setting.frequency_penalty,
                presence_penalty=self._config.model.setting.presence_penalty,
            ),
            model=OpenAIChatCompletionsModel(
                model=self._config.model.name,
                openai_client=await ai_proxy_client_factory.get_client(self._config.model.model_publisher),
            ),
            output_type=EvaluationFeedback,
            instructions=self._config.prompt,
        )
        return agent

    def meets_threshold(self, result: EvaluationFeedback) -> bool:
        """判断评估结果是否达到通过阈值"""
        if self._config.pass_threshold is None:
            return True  # 如果没有设置阈值，默认通过
        return result.score >= self._config.pass_threshold

    async def run_agent(self, list_input: list[TResponseInputItem]) -> EvaluationFeedback:
        self._index += 1
        logger.info(f"################ evaluation index: {self._index}")
        start_time = time.time()
        if self._agent is None:
            self._agent = await self._create_agent()
        evaluator_result = await Runner.run(self._agent, list_input)
        result: EvaluationFeedback = evaluator_result.final_output
        logger.info(f"################ evaluation result: {result}")
        logger.info(f"################ evaluation total cost: {round(time.time() - start_time, 2)}")
        return result
