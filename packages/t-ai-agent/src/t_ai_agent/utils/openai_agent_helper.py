from copy import deepcopy

from agents import (
    Agent,
    FunctionTool,
    function_tool,
)
from agents.mcp import MCPServer
from agents.tool_context import Tool<PERSON>ontext
from loguru import logger
from t_ai_common.utils.error_helper import raise_root_cause_err

from ..model.agent_context import AgentExecutionContext
from ..model.agent_dsl import McpTool

TOOL_AGENT_SYSTEM_PROMPT = """
你是一个工具代理，你需要根据提供的指示(input)，调用相关工具，返回对应的结果。
"""


class OpenaiAgentHelper:
    @staticmethod
    def mcp_server_as_tool(mcp_tool_config: McpTool, mcp_server: MCPServer, ref_agent: Agent) -> FunctionTool:
        """
        mcp_server_as_tool
        wrap mcp_server into agent as tool
        which can be used to reduce the total amount of main agent

        Args:
            mcp_tool_config (McpTool): mcp tool config
            mcp_server (MCPServer): mcp server
            ref_agent (Agent): ref agent

        Returns:
            FunctionTool: function tool(agent as tool)
        """

        @function_tool(
            name_override=mcp_tool_config.name,
            description_override=mcp_tool_config.desc,
        )
        async def _tool(context: ToolContext, input: str) -> str | None:
            from agents.run import Runner

            # tool_agent_model share with ref_agent
            tool_agent_model = ref_agent.model
            # new_model_settings
            model_settings = deepcopy(ref_agent.model_settings)
            # required, which requires the LLM to use a tool (but it can intelligently decide which tool).
            # perhaps openai models only work
            # refer: https://openai.github.io/openai-agents-python/agents/#forcing-tool-use
            model_settings.tool_choice = "required"
            # tool_use_behavior
            # The output of the first tool call is used as the final output. This means that the LLM does not process the result of the tool call.
            tool_use_behavior = "stop_on_first_tool"

            tool_call_id = context.tool_call_id
            agent_exec_ctx: AgentExecutionContext = context.context
            try:
                agent = Agent(
                    name=mcp_tool_config.name,
                    model=tool_agent_model,
                    model_settings=model_settings,
                    instructions=TOOL_AGENT_SYSTEM_PROMPT,
                    mcp_servers=[mcp_server],
                    tool_use_behavior=tool_use_behavior,
                )
                output = await Runner.run(
                    starting_agent=agent,
                    input=input,
                    context=context.context,
                )
                agent_exec_ctx.update_tool_call_status(tool_call_id, "success")
                output = str(output.final_output)
                logger.info(f"tool_agent[{mcp_tool_config.name}] output: {output}")
                return output
            except Exception as e:
                logger.exception(f"Error running mcp_server_as_tool: {e}")

                agent_exec_ctx.update_tool_call_status(tool_call_id, "error")

                raise_root_cause_err(e)

        return _tool
