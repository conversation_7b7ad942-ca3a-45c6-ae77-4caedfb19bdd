"""
Monkey Patching 覆盖 agents.mcp.util.invoke_mcp_tool 方法
"""
import json
import time
import uuid
from typing import Any

from agents import AgentsException, FunctionSpanData, ModelBehaviorError, get_current_span
from loguru import logger


def patch_invoke_mcp_tool():
    """应用 invoke_mcp_tool 方法的 Monkey Patching"""
    try:
        from agents.mcp.util import MCPUtil

        # 保存原始方法
        original_invoke_mcp_tool = MCPUtil.invoke_mcp_tool

        @classmethod
        async def custom_invoke_mcp_tool(
                cls, server, tool, context, input_json: str
        ) -> str:
            """自定义的 invoke_mcp_tool 方法"""
            from t_ai_agent.model import AgentExecutionContext
            from t_ai_agent.model.agent_context import ToolCallMetadata

            tool_call_id = str(uuid.uuid4())
            agent_exec_ctx: AgentExecutionContext = context.context
            tool_key = tool.name
            agent_name = tool.meta.get("agent_name") if tool.meta else None

            try:
                # 这里做判断是要排除掉mcp发送事件的工具信息，因为这里没有call_id，所有用这个来判断
                if agent_name and agent_exec_ctx.current_agent.name != agent_name:
                    # 这里手动添加工具信息，是因为当agent_as_tools后，工具的信息无法透出
                    agent_exec_ctx.manual_add_tool_call_mapping(
                        tool_call_id,
                        ToolCallMetadata(key=tool_key, arguments=input_json, create_time=time.time()),
                        agent_name
                    )

                try:
                    json_data: dict[str, Any] = json.loads(input_json) if input_json else {}
                except Exception as e:
                    logger.debug(f"Invalid JSON input for tool {tool.name}: {input_json}")
                    raise ModelBehaviorError(
                        f"Invalid JSON input for tool {tool.name}: {input_json}"
                    ) from e

                logger.debug(f"Invoking MCP tool {tool.name} with input {input_json}")

                try:
                    result = await server.call_tool(tool.name, json_data)
                except Exception as e:
                    logger.error(f"Error invoking MCP tool {tool.name}: {e}")
                    raise AgentsException(f"Error invoking MCP tool {tool.name}: {e}") from e

                logger.debug(f"MCP tool {tool.name} returned {result}")

                # If structured content is requested and available, use it exclusively
                if server.use_structured_content and result.structuredContent:
                    tool_output = json.dumps(result.structuredContent)
                else:
                    # Fall back to regular text content processing
                    # The MCP tool result is a list of content items, whereas OpenAI tool
                    # outputs are a single string. We'll try to convert.
                    if len(result.content) == 1:
                        tool_output = result.content[0].model_dump_json()
                    elif len(result.content) > 1:
                        tool_results = [item.model_dump(mode="json") for item in result.content]
                        tool_output = json.dumps(tool_results)
                    else:
                        # Empty content is a valid result (e.g., "no results found")
                        tool_output = "[]"

                # 更新工具调用状态为成功
                agent_exec_ctx.update_tool_call_status(tool_call_id, "success", tool_output)
            except Exception as ep:
                # 更新工具调用状态为错误
                agent_exec_ctx.update_tool_call_status(tool_call_id, "error", str(ep))
                raise

            current_span = get_current_span()
            if current_span:
                if isinstance(current_span.span_data, FunctionSpanData):
                    current_span.span_data.output = tool_output
                    current_span.span_data.mcp_data = {
                        "server": server.name,
                    }
                else:
                    logger.warning(
                        f"Current span is not a FunctionSpanData, skipping tool output: {current_span}"
                    )

            return tool_output

        # 应用补丁
        setattr(MCPUtil, 'invoke_mcp_tool', custom_invoke_mcp_tool)
        logger.info("✅ invoke_mcp_tool 方法 Monkey Patching 应用成功")

        return original_invoke_mcp_tool

    except ImportError as e:
        logger.error(f"❌ 无法导入 agents.mcp.util 模块: {e}")
        return None
    except Exception as e:
        logger.error(f"❌ 应用 invoke_mcp_tool Monkey Patching 失败: {e}")
        return None


def restore_invoke_mcp_tool(original_method):
    """恢复原始的 invoke_mcp_tool 方法"""
    if original_method is None:
        logger.warning("⚠️ 没有原始方法可以恢复")
        return

    try:
        from agents.mcp.util import MCPUtil
        setattr(MCPUtil, 'invoke_mcp_tool', original_method)
        logger.info("✅ invoke_mcp_tool 方法已恢复")
    except ImportError as e:
        logger.error(f"❌ 无法导入 agents.mcp.util 模块: {e}")
    except Exception as e:
        logger.error(f"❌ 恢复 invoke_mcp_tool 方法失败: {e}")


# 全局变量存储原始方法
_original_invoke_mcp_tool = None


def apply_patch():
    """应用补丁"""
    global _original_invoke_mcp_tool
    _original_invoke_mcp_tool = patch_invoke_mcp_tool()


def remove_patch():
    """移除补丁"""
    global _original_invoke_mcp_tool
    restore_invoke_mcp_tool(_original_invoke_mcp_tool)
    _original_invoke_mcp_tool = None
