from typing import Any, Optional
from pydantic import Field

from ..tool.tool_call_execution import ToolCallExecutionModel


class ProprietaryToolDefinition(ToolCallExecutionModel):
    """专有工具定义模型"""
    description: Optional[str] = None
    implementation: Optional["Implementation"] = Field(None, alias="implementation")
    tool_schema: Optional["ToolSchema"] = Field(None, alias="toolSchema")

    class Implementation(ToolCallExecutionModel):
        """工具实现"""
        code: Optional[str] = None
        encoding: Optional[str] = Field("base64", alias="encoding")
        language: Optional[str] = Field("python", alias="language")

    class ToolSchema(ToolCallExecutionModel):
        """工具Schema"""
        properties: Optional[dict[str, Any]] = Field(None, alias="properties")
        required: Optional[list[str]] = Field(None, alias="required")
        type: Optional[str] = Field("object", alias="type")
