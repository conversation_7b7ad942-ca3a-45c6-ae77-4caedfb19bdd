from typing import Optional

from pydantic import BaseModel, Field

from t_ai_agent.model.agent_dsl import LlmModel

class ResultValidationConfig(BaseModel):
    enabled: bool = Field(False, alias="enabled")
    model: Optional[LlmModel] = Field(None, alias="model")
    prompt: Optional[str] = Field(None, alias="prompt")
    pass_threshold: Optional[float] = Field(None, alias="passThreshold")
    max_retries: Optional[int] = Field(default=1, alias="maxRetries")
