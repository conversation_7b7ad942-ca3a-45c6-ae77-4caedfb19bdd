from datetime import datetime
from typing import Any, Optional, Union

from agents import ToolCallOutputItem
from openai.types.responses import ResponseFunction<PERSON><PERSON><PERSON>all
from pydantic import BaseModel, Field

from t_ai_app.ctx import ReqCtx
from .agent_context import AgentExecutionContext
from .agent_dsl import Agent<PERSON><PERSON>
from .common import TokenUsage
from ..message_i18n import get_i18n_message
from t_ai_agent.agent_tool_visible import get_tool_call_visible, get_tool_call_arguments_visible, \
    get_tool_call_output_visible


class AgentMessageMeta(BaseModel):
    key: Optional[str] = None
    name: Optional[str] = None
    avatar: Optional[str] = None
    llm: Optional[str] = None

    @classmethod
    def from_agent_meta(cls, agent_meta: AgentMeta):
        return cls(
            key=agent_meta.key,
            name=agent_meta.name,
            avatar=agent_meta.props.avatar,
            llm=agent_meta.props.model.name
        )

    @classmethod
    def from_agent(cls, agent, context: AgentExecutionContext):
        agents = context.agents
        agent_name = None
        agent_key = None
        agent_avatar = None
        model_name = None
        for agent_item in agents:
            if agent.name == agent_item.name:
                agent_name = agent_item.name
                agent_key = agent_item.key
                agent_avatar = agent_item.props.avatar
                model_name = agent_item.props.model.name
                break
        return cls(
            key=getattr(agent, "key", agent_key),
            name=getattr(agent, "name", agent_name),
            avatar=agent_avatar,
            llm=model_name
        )


class AgentReasoningContent(BaseModel):
    type: str = "reasoning"
    text: Optional[str] = None
    status: str = "DOING"

    @classmethod
    def from_event(cls, event, context):
        if type(event) is str:
            return cls(text=event, status="DONE")
        return cls(text=event.delta if hasattr(event, "delta") else str(event))


class AgentHandoffContent(BaseModel):
    type: str = "agent_handoff"
    text: Optional[str] = None
    target_agent: AgentMessageMeta = Field(default_factory=AgentMessageMeta)

    @classmethod
    def from_event(cls, event, context):
        if isinstance(event, AgentMeta):
            target_agent = AgentMessageMeta.from_agent(event, context)
        else:
            target_agent = AgentMessageMeta.from_agent(event.target_agent, context)
        content_text = get_i18n_message("handoff_content", ReqCtx.get_lang(), target_agent_name=target_agent.name)
        return cls(text=content_text, target_agent=target_agent)


class AgentToolCallContent(BaseModel):
    type: str = "function_call"
    key: Optional[str] = None
    name: Optional[str] = None
    arguments: Optional[str] = None
    call_id: Optional[str] = None
    status: str = "DONE"
    visible: Optional[bool] = True

    @classmethod
    def from_event(cls, event: ResponseFunctionToolCall, context: AgentExecutionContext):
        call_id = event.call_id
        arguments = event.arguments
        tool_call_mapping = context.tools_call_mapping.get(call_id)

        tool_key = tool_call_mapping.key
        tool_name = tool_call_mapping.name
        # 针对知识库搜索工具做i18n
        if tool_key == "knowledge_base_search":
            i18n_key = "tool_name_" + tool_key
            tool_name = get_i18n_message(i18n_key, ReqCtx.get_lang())
        
        # 根据tool_visible配置设置visible属性
        visible = get_tool_call_visible(tool_call_mapping.tool_visible)

        return cls(key=tool_key, name=tool_name, arguments='', call_id=call_id, visible=visible)


class AgentToolCallArgumentsContent(AgentToolCallContent):
    type: str = "function_call_arguments"

    @classmethod
    def from_event(cls, event: "ToolCallOutputItem", context: AgentExecutionContext):
        call_id = event.raw_item.get("call_id")
        # tool_call_mapping一定有值，因为上层判断过了
        tool_call_mapping = context.tools_call_mapping.get(call_id)
        tool_key = tool_call_mapping.key
        tool_name = tool_call_mapping.name
        arguments = tool_call_mapping.arguments

        # 根据tool_visible配置设置visible属性
        visible = get_tool_call_arguments_visible(tool_call_mapping.tool_visible)

        return cls(key=tool_key, name=tool_name, arguments=arguments, call_id=call_id, visible=visible)


class AgentToolOutputContent(BaseModel):
    type: str = "function_call_output"
    call_id: Optional[str] = None
    output: Optional[Any] = None
    invoke_status: Optional[str] = "success"
    invoke_cost_time: Optional[float] = None
    invoke_cost_token: TokenUsage = TokenUsage()
    status: str = "DONE"
    visible: Optional[bool] = True

    @classmethod
    def from_event(cls, event: ToolCallOutputItem, context: AgentExecutionContext):
        tool_call_info = context.tools_call_mapping.get(event.raw_item.get("call_id"))
        
        # 根据tool_visible配置设置visible属性
        visible = get_tool_call_output_visible(tool_call_info.tool_visible)

        if tool_call_info is not None:
            return cls(
                call_id=event.raw_item.get("call_id"),
                output=event.raw_item.get("output"),
                visible=visible,
                invoke_status=tool_call_info.invoke_status,
                invoke_cost_time=tool_call_info.invoke_cost_time,
                invoke_cost_token=tool_call_info.invoke_cost_token,
            )
        else:
            return cls(
                call_id=event.raw_item.get("call_id"),
                output=event.raw_item.get("output"),
                visible=visible,
            )


class AgentTokenUsageContent(BaseModel):
    type: str = "tokens"
    usage: Optional[TokenUsage] = None
    cost_time: Optional[float] = None
    status: str = "DONE"

    @classmethod
    def from_event(cls, event, context: AgentExecutionContext):
        instance = cls(
            usage=TokenUsage(
                input_tokens=event.input_tokens,
                output_tokens=event.output_tokens,
                total_tokens=event.total_tokens
            )
        )
        if context.cost_time is not None:
            instance.cost_time = context.cost_time
            return instance
        else:
            return instance


class AgentTextContent(BaseModel):
    type: str = "text"
    text: Optional[str] = None
    status: str = "DOING"

    @classmethod
    def from_event(cls, event, context):
        if type(event) is str:
            return cls(text=event, status="DONE")
        return cls(text=event.delta if hasattr(event, "delta") else str(event))


class AgentErrorContent(BaseModel):
    type: str = "error"
    code: Optional[int] = 500
    message: str = "AI开小差了，请稍后重试"
    details: Optional[str] = None
    status: str = "DONE"

    @classmethod
    def from_event(cls, exception, context):
        message = get_i18n_message("error_content", ReqCtx.get_lang())
        return cls(message=message, details=str(exception))


class AgentCallbackContent(BaseModel):
    type: str = "callback"
    callback_type: str = "service"
    callback_url: str
    arguments: Optional[str] = None
    status: str = "DONE"

    @classmethod
    def from_event(cls, event, context):
        return cls(
            callback_type=event.get("callback_type"),
            callback_url=event.get("callback_url"),
            arguments=event.get("arguments"),
        )


class TaskStartContent(BaseModel):
    type: str = "task_start"
    status: str = "DONE"
    task_id: str
    task_name: str
    new_input: str
    arguments: str = ""


class AgentMessage(BaseModel):
    # message_id: Optional[str] = None
    # parent_id: Optional[str] = None
    # conversation_id: Optional[str] = None
    created_at: Optional[float] = Field(default_factory=lambda: datetime.now().timestamp())
    role: str = "assistant"
    content: Optional[
        Union[
            AgentReasoningContent,
            AgentHandoffContent,
            AgentToolCallContent,
            AgentToolCallArgumentsContent,
            AgentToolOutputContent,
            AgentTokenUsageContent,
            AgentErrorContent,
            AgentTextContent,
            AgentCallbackContent,
            TaskStartContent,
        ]
    ] = None
    meta: Optional[AgentMessageMeta] = None

    @classmethod
    def from_event(cls, event, content_type: str, context: AgentExecutionContext, meta: AgentMessageMeta | None = None):
        instance = cls()

        if meta is not None:
            instance.meta = meta
        elif hasattr(event, "agent"):
            instance.meta = AgentMessageMeta.from_agent(event.agent, context)
        else:
            if context.current_agent is not None:
                instance.meta = AgentMessageMeta.from_agent(context.current_agent, context)

        content_class = {
            "reasoning": AgentReasoningContent,
            "handoff": AgentHandoffContent,
            "function_call": AgentToolCallContent,
            "function_call_arguments": AgentToolCallArgumentsContent,
            "function_call_output": AgentToolOutputContent,
            "tokens": AgentTokenUsageContent,
            "error": AgentErrorContent,
            "text": AgentTextContent,
            "callback": AgentCallbackContent,
            "task_start": TaskStartContent,
        }.get(content_type)

        if content_class:
            instance.content = content_class.from_event(event, context)

        return instance
