# 专有工具Redis缓存功能

本文档介绍如何使用Redis缓存来存储和获取专有工具定义，并在专有工具执行过程中从Redis加载Python代码。

## 功能概述

专有工具Redis缓存功能提供了以下能力：

1. **缓存存储**: 将专有工具定义（包括Python代码、工具Schema等）存储到Redis
2. **缓存获取**: 从Redis获取专有工具定义并转换为`ProprietaryToolDefinition`对象
3. **代码解码**: 支持Base64编码的Python代码自动解码
4. **沙箱执行**: 在安全的沙箱环境中执行从Redis获取的Python代码

## 配置

### 1. Redis配置

在`.env`文件中配置Redis连接信息：

```env
# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0
REDIS_DECODE_RESPONSES=true
REDIS_SOCKET_TIMEOUT=5
REDIS_SOCKET_CONNECT_TIMEOUT=5
```

### 2. 依赖安装

确保安装了Redis客户端库：

```bash
pip install redis>=5.0.0
```

## 核心组件

### 1. RedisClient

位置：`t_ai_common.clients.redis_client`

提供Redis基础操作功能：
- `get(key)`: 获取字符串值
- `set(key, value, ex=None)`: 设置字符串值
- `get_json(key)`: 获取JSON值
- `set_json(key, value, ex=None)`: 设置JSON值
- `ping()`: 测试连接

### 2. ProprietaryToolCache

位置：`t_ai_agent.utils.proprietary_tool_cache`

专有工具缓存服务：
- `get_tool_definition(cache_key)`: 从Redis获取工具定义
- `set_tool_definition(cache_key, tool_definition, ex=None)`: 存储工具定义到Redis
- `get_decoded_code(tool_definition)`: 获取解码后的Python代码

### 3. ProprietaryToolDefinition

位置：`t_ai_agent.tool.proprietary_tool_execution`

专有工具定义数据模型：
```python
class ProprietaryToolDefinition:
    description: Optional[str]
    implementation: Optional[Implementation]
    tool_schema: Optional[ToolSchema]
    
    class Implementation:
        code: Optional[str]
        encoding: Optional[str] = "base64"
        language: Optional[str] = "python"
    
    class ToolSchema:
        properties: Optional[dict]
        required: Optional[list[str]]
        type: Optional[str] = "object"
```

## 使用方法

### 1. 存储专有工具到Redis

```python
from t_ai_agent.utils.proprietary_tool_helper import store_sample_tool_to_redis

# Python代码
python_code = '''
def main(arguments):
    message = arguments.get("message", "Hello World!")
    return {"result": f"处理后的消息: {message.upper()}"}

if __name__ == "__main__":
    result = main(arguments)
'''

# 存储到Redis
cache_key = "agent:proprietary_tool:TEAM:USER:AGENT:TOOL"
success = store_sample_tool_to_redis(
    cache_key=cache_key,
    python_code=python_code,
    description="示例专有工具"
)
```

### 2. 从Redis获取专有工具定义

```python
from t_ai_agent.utils.proprietary_tool_helper import get_tool_from_redis

# 从Redis获取
tool_definition = get_tool_from_redis(cache_key)
if tool_definition:
    print(f"工具描述: {tool_definition.description}")
    print(f"编码方式: {tool_definition.implementation.encoding}")
```

### 3. 解码Python代码

```python
from t_ai_agent.utils.proprietary_tool_cache import proprietary_tool_cache

# 解码代码
decoded_code = proprietary_tool_cache.get_decoded_code(tool_definition)
if decoded_code:
    print(f"Python代码:\n{decoded_code}")
```

## 缓存键格式

专有工具的缓存键遵循以下格式：

```
agent:proprietary_tool:{team_code}:{user_id}:{agent_key}:{tool_key}
```

示例：
```
agent:proprietary_tool:PJ0724:440724569185477:ERP_FIN$agent_reimbursement:check_expense_report
```

## 执行流程

当专有工具被调用时，执行流程如下：

1. **构建缓存键**: 根据团队代码、用户ID、Agent键和工具键构建缓存键
2. **获取工具定义**: 从Redis获取专有工具定义
3. **解码Python代码**: 将Base64编码的代码解码为纯文本
4. **解析参数**: 解析工具调用参数
5. **沙箱执行**: 在安全沙箱中执行Python代码
6. **返回结果**: 返回执行结果或错误信息

## 错误处理

系统会处理以下错误情况：

- **Redis连接失败**: 记录错误日志，返回连接失败信息
- **缓存键不存在**: 返回"未找到专有工具定义"错误
- **代码解码失败**: 返回"无法解码Python代码"错误
- **代码执行失败**: 返回沙箱执行错误信息

## 安全考虑

1. **沙箱执行**: 所有Python代码都在受限的沙箱环境中执行
2. **资源限制**: 沙箱有CPU时间和内存限制
3. **模块限制**: 只允许导入安全的模块
4. **编码验证**: 对Base64编码进行验证

## 示例代码

完整的使用示例请参考：
- `packages/t-ai-agent/examples/proprietary_tool_redis_example.py`

运行示例：
```bash
cd packages/t-ai-agent
python examples/proprietary_tool_redis_example.py
```

## 故障排除

### 1. Redis连接失败

检查Redis服务是否启动：
```bash
redis-cli ping
```

检查配置是否正确：
```bash
echo $REDIS_HOST
echo $REDIS_PORT
```

### 2. 缓存数据不存在

使用Redis CLI检查数据：
```bash
redis-cli
> GET "agent:proprietary_tool:TEAM:USER:AGENT:TOOL"
```

### 3. 代码执行失败

检查Python代码语法和沙箱限制，确保：
- 代码语法正确
- 不使用被禁止的模块
- 设置了`result`变量作为返回值
