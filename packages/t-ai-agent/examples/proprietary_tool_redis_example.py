"""
专有工具Redis缓存使用示例
"""
import asyncio
import base64
from t_ai_agent.utils.proprietary_tool_helper import (
    store_sample_tool_to_redis,
    get_tool_from_redis,
    test_redis_connection,
    SAMPLE_PYTHON_CODE
)
from t_ai_agent.utils.proprietary_tool_cache import proprietary_tool_cache


async def main():
    """主函数"""
    print("=== 专有工具Redis缓存使用示例 ===\n")
    
    # 1. 测试Redis连接
    print("1. 测试Redis连接...")
    if test_redis_connection():
        print("✅ Redis连接正常")
    else:
        print("❌ Redis连接失败，请检查Redis服务是否启动")
        return
    
    # 2. 存储示例专有工具到Redis
    cache_key = "agent:proprietary_tool:TEST:USER123:test_agent:sample_tool"
    print(f"\n2. 存储示例专有工具到Redis...")
    print(f"   Cache Key: {cache_key}")
    
    success = store_sample_tool_to_redis(
        cache_key=cache_key,
        python_code=SAMPLE_PYTHON_CODE,
        description="这是一个示例专有工具，用于演示Redis缓存功能"
    )
    
    if success:
        print("✅ 专有工具存储成功")
    else:
        print("❌ 专有工具存储失败")
        return
    
    # 3. 从Redis获取专有工具定义
    print(f"\n3. 从Redis获取专有工具定义...")
    tool_definition = get_tool_from_redis(cache_key)
    
    if tool_definition:
        print("✅ 成功获取专有工具定义")
        print(f"   描述: {tool_definition.description}")
        print(f"   编码方式: {tool_definition.implementation.encoding}")
        print(f"   语言: {tool_definition.implementation.language}")
        print(f"   Schema类型: {tool_definition.tool_schema.type}")
        print(f"   必需参数: {tool_definition.tool_schema.required}")
    else:
        print("❌ 获取专有工具定义失败")
        return
    
    # 4. 解码Python代码
    print(f"\n4. 解码Python代码...")
    decoded_code = proprietary_tool_cache.get_decoded_code(tool_definition)
    
    if decoded_code:
        print("✅ 成功解码Python代码")
        print(f"   代码长度: {len(decoded_code)} 字符")
        print("   代码预览:")
        lines = decoded_code.split('\n')
        for i, line in enumerate(lines[:10]):  # 只显示前10行
            print(f"     {i+1:2d}: {line}")
        if len(lines) > 10:
            print(f"     ... (还有 {len(lines) - 10} 行)")
    else:
        print("❌ 解码Python代码失败")
        return
    
    # 5. 验证存储的数据
    print(f"\n5. 验证存储的数据...")
    
    # 直接从Redis获取原始数据
    raw_data = proprietary_tool_cache.redis_client.get_json(cache_key)
    if raw_data:
        print("✅ 原始数据获取成功")
        print(f"   数据结构: {list(raw_data.keys())}")
        
        # 验证Base64编码
        if 'implementation' in raw_data and 'code' in raw_data['implementation']:
            encoded_code = raw_data['implementation']['code']
            try:
                decoded_bytes = base64.b64decode(encoded_code)
                decoded_text = decoded_bytes.decode('utf-8')
                print(f"   Base64解码验证: ✅ 成功")
                print(f"   解码后代码长度: {len(decoded_text)} 字符")
            except Exception as e:
                print(f"   Base64解码验证: ❌ 失败 - {e}")
    else:
        print("❌ 原始数据获取失败")
    
    print(f"\n=== 示例完成 ===")


if __name__ == "__main__":
    asyncio.run(main())
