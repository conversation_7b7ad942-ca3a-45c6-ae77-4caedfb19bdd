"""
简单的Python代码校验示例
直接复用 PythonSandbox 的校验功能
"""
import sys
import os

# 添加包路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from t_ai_agent.utils.code_validator import (
    validate_python_code_simple,
    validate_code_from_cache,
    strict_validator,
    standard_validator,
    permissive_validator,
    get_default_allowed_modules
)


def test_simple_validation():
    """测试简单的代码校验"""
    print("=== 简单代码校验测试 ===\n")
    
    test_cases = [
        {
            "name": "正常代码",
            "code": """
def main(arguments):
    import json
    import datetime
    
    message = arguments.get('message', 'Hello!')
    result = {
        'message': message,
        'timestamp': datetime.datetime.now().isoformat()
    }
    return json.dumps(result)
""",
        },
        {
            "name": "语法错误",
            "code": """
def main(arguments):
    message = arguments.get('message', 'Hello!'
    return message
""",
        },
        {
            "name": "不安全操作",
            "code": """
import os
def main(arguments):
    os.system('rm -rf /')
    return 'done'
""",
        },
        {
            "name": "不允许的模块",
            "code": """
import subprocess
def main(arguments):
    result = subprocess.run(['ls'], capture_output=True)
    return result.stdout.decode()
""",
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"{i}. 测试: {test_case['name']}")
        
        is_valid, error_msg = validate_python_code_simple(test_case['code'])
        
        if is_valid:
            print("   ✅ 校验通过")
        else:
            print(f"   ❌ 校验失败: {error_msg}")
        
        print()


def test_predefined_validators():
    """测试预定义的校验器"""
    print("=== 预定义校验器测试 ===\n")
    
    code_with_requests = """
import requests
import json

def main(arguments):
    url = arguments.get('url', 'https://api.example.com')
    response = requests.get(url)
    return json.dumps({'status': response.status_code})
"""
    
    print("测试代码包含 requests 模块:")
    print(f"代码: {code_with_requests[:100]}...")
    print()
    
    # 严格校验器
    print("1. 严格校验器 (只允许基本模块):")
    is_valid, error = strict_validator(code_with_requests)
    print(f"   结果: {'通过' if is_valid else '失败'}")
    if not is_valid:
        print(f"   错误: {error}")
    
    # 标准校验器
    print("\n2. 标准校验器 (允许常用标准库):")
    is_valid, error = standard_validator(code_with_requests)
    print(f"   结果: {'通过' if is_valid else '失败'}")
    if not is_valid:
        print(f"   错误: {error}")
    
    # 宽松校验器
    print("\n3. 宽松校验器 (允许第三方库):")
    is_valid, error = permissive_validator(code_with_requests)
    print(f"   结果: {'通过' if is_valid else '失败'}")
    if not is_valid:
        print(f"   错误: {error}")


def test_custom_modules():
    """测试自定义模块列表"""
    print("\n=== 自定义模块列表测试 ===\n")
    
    code = """
import numpy as np
import pandas as pd

def main(arguments):
    data = np.array([1, 2, 3, 4, 5])
    df = pd.DataFrame({'values': data})
    return df.to_json()
"""
    
    print("测试代码包含 numpy 和 pandas:")
    print()
    
    # 默认校验（不允许）
    print("1. 默认校验:")
    is_valid, error = validate_python_code_simple(code)
    print(f"   结果: {'通过' if is_valid else '失败'}")
    if not is_valid:
        print(f"   错误: {error}")
    
    # 自定义允许列表
    print("\n2. 自定义允许 numpy 和 pandas:")
    custom_modules = get_default_allowed_modules() + ['numpy', 'pandas']
    is_valid, error = validate_python_code_simple(code, custom_modules)
    print(f"   结果: {'通过' if is_valid else '失败'}")
    if not is_valid:
        print(f"   错误: {error}")


def demo_cache_validation():
    """演示从缓存校验代码（需要Redis运行）"""
    print("\n=== 缓存代码校验演示 ===\n")
    
    # 这个演示需要Redis运行和有效的缓存数据
    cache_key = "agent:proprietary_tool:TEST:USER123:test_agent:sample_tool"
    
    print(f"尝试从缓存校验代码: {cache_key}")
    
    try:
        is_valid, error_msg, decoded_code = validate_code_from_cache(cache_key)
        
        if decoded_code:
            print(f"✅ 成功获取代码，长度: {len(decoded_code)} 字符")
            print(f"校验结果: {'通过' if is_valid else '失败'}")
            if not is_valid:
                print(f"错误: {error_msg}")
        else:
            print(f"❌ 无法获取代码: {error_msg}")
            
    except Exception as e:
        print(f"❌ 缓存校验异常: {e}")
        print("   (这是正常的，如果Redis未运行或缓存中没有数据)")


if __name__ == "__main__":
    test_simple_validation()
    test_predefined_validators()
    test_custom_modules()
    demo_cache_validation()
    
    print("\n🎉 所有测试完成！")
    print("\n总结:")
    print("- ✅ 直接复用了 PythonSandbox._is_safe_code() 方法")
    print("- ✅ 提供了简单易用的校验接口")
    print("- ✅ 支持自定义允许模块列表")
    print("- ✅ 提供了预定义的校验器（严格/标准/宽松）")
    print("- ✅ 支持从Redis缓存校验代码")
