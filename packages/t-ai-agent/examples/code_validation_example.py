"""
Python代码校验功能使用示例
"""
import asyncio
import sys
import os

# 添加包路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from t_ai_agent.sandbox.sandbox_service import validate_python_code
from t_ai_agent.sandbox.model import ValidateCodeRequest


async def test_code_validation():
    """测试代码校验功能"""
    print("=== Python代码校验功能测试 ===\n")
    
    # 测试用例
    test_cases = [
        {
            "name": "正常的Python代码",
            "code": """
def main(arguments):
    import json
    import datetime
    
    message = arguments.get('message', 'Hello World!')
    result = {
        'message': message,
        'timestamp': datetime.datetime.now().isoformat(),
        'status': 'success'
    }
    return json.dumps(result, ensure_ascii=False)

if __name__ == "__main__":
    result = main({'message': '测试消息'})
""",
            "description": "包含合法导入和函数定义的正常代码"
        },
        
        {
            "name": "语法错误的代码",
            "code": """
def main(arguments):
    message = arguments.get('message', 'Hello World!'
    # 缺少右括号
    return message
""",
            "description": "包含语法错误的代码"
        },
        
        {
            "name": "不安全的代码",
            "code": """
import os
import sys

def main(arguments):
    # 尝试执行系统命令
    os.system('ls -la')
    
    # 尝试访问文件系统
    with open('/etc/passwd', 'r') as f:
        content = f.read()
    
    return content
""",
            "description": "包含不安全操作的代码"
        },
        
        {
            "name": "不允许的模块导入",
            "code": """
import subprocess
import socket
import threading

def main(arguments):
    # 尝试使用不允许的模块
    result = subprocess.run(['echo', 'hello'], capture_output=True)
    return result.stdout.decode()
""",
            "description": "导入了不允许的模块"
        },
        
        {
            "name": "复杂但安全的代码",
            "code": """
import json
import datetime
import math
import random
from typing import Dict, List, Any

def calculate_statistics(data: List[float]) -> Dict[str, float]:
    '''计算数据统计信息'''
    if not data:
        return {}
    
    return {
        'count': len(data),
        'sum': sum(data),
        'mean': sum(data) / len(data),
        'min': min(data),
        'max': max(data),
        'std_dev': math.sqrt(sum((x - sum(data)/len(data))**2 for x in data) / len(data))
    }

def main(arguments: Dict[str, Any]) -> str:
    '''主函数'''
    data = arguments.get('data', [])
    
    if not isinstance(data, list):
        return json.dumps({'error': '数据必须是列表格式'})
    
    try:
        # 转换为浮点数
        float_data = [float(x) for x in data]
        
        # 计算统计信息
        stats = calculate_statistics(float_data)
        
        # 添加时间戳
        stats['timestamp'] = datetime.datetime.now().isoformat()
        stats['random_id'] = random.randint(1000, 9999)
        
        return json.dumps(stats, ensure_ascii=False, indent=2)
        
    except (ValueError, TypeError) as e:
        return json.dumps({'error': f'数据处理错误: {str(e)}'})

if __name__ == "__main__":
    test_data = {'data': [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]}
    result = main(test_data)
""",
            "description": "复杂但完全安全的数据处理代码"
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"{i}. 测试: {test_case['name']}")
        print(f"   描述: {test_case['description']}")
        
        # 创建校验请求
        request = ValidateCodeRequest(
            code=test_case['code'],
            check_syntax=True,
            check_security=True,
            check_imports=True
        )
        
        # 执行校验
        try:
            result = await validate_python_code(request)
            
            # 显示结果
            print(f"   ✅ 校验完成")
            print(f"   总体有效性: {'✅ 通过' if result.is_valid else '❌ 失败'}")
            print(f"   语法检查: {'✅ 通过' if result.syntax_valid else '❌ 失败'}")
            print(f"   安全检查: {'✅ 通过' if result.security_valid else '❌ 失败'}")
            print(f"   导入检查: {'✅ 通过' if result.imports_valid else '❌ 失败'}")
            
            if result.errors:
                print(f"   错误信息:")
                for error in result.errors:
                    print(f"     - {error}")
            
            if result.warnings:
                print(f"   警告信息:")
                for warning in result.warnings:
                    print(f"     - {warning}")
            
            # 显示代码详情
            details = result.details
            print(f"   代码详情:")
            print(f"     - 行数: {details.get('line_count', 0)}")
            print(f"     - 字符数: {details.get('char_count', 0)}")
            print(f"     - 函数: {details.get('functions', [])}")
            print(f"     - 类: {details.get('classes', [])}")
            print(f"     - 导入: {details.get('imports', [])}")
            print(f"     - 包含main函数: {'是' if details.get('has_main', False) else '否'}")
            
        except Exception as e:
            print(f"   ❌ 校验异常: {e}")
        
        print()
    
    print("🎉 代码校验测试完成！")


async def test_custom_validation():
    """测试自定义校验选项"""
    print("=== 自定义校验选项测试 ===\n")
    
    code = """
import requests
import numpy as np

def main(arguments):
    url = arguments.get('url', 'https://api.example.com')
    response = requests.get(url)
    data = np.array([1, 2, 3, 4, 5])
    return {'status': response.status_code, 'data_sum': np.sum(data)}
"""
    
    # 测试1: 默认校验（不允许requests和numpy）
    print("1. 默认校验（严格模式）:")
    request1 = ValidateCodeRequest(code=code)
    result1 = await validate_python_code(request1)
    print(f"   结果: {'通过' if result1.is_valid else '失败'}")
    if result1.errors:
        for error in result1.errors:
            print(f"   错误: {error}")
    
    # 测试2: 允许特定模块
    print("\n2. 允许requests和numpy模块:")
    request2 = ValidateCodeRequest(
        code=code,
        allowed_modules=['json', 'datetime', 'requests', 'numpy']
    )
    result2 = await validate_python_code(request2)
    print(f"   结果: {'通过' if result2.is_valid else '失败'}")
    if result2.errors:
        for error in result2.errors:
            print(f"   错误: {error}")
    
    # 测试3: 只检查语法，不检查安全性和导入
    print("\n3. 只检查语法:")
    request3 = ValidateCodeRequest(
        code=code,
        check_syntax=True,
        check_security=False,
        check_imports=False
    )
    result3 = await validate_python_code(request3)
    print(f"   结果: {'通过' if result3.is_valid else '失败'}")
    
    print("\n🎉 自定义校验测试完成！")


if __name__ == "__main__":
    asyncio.run(test_code_validation())
    asyncio.run(test_custom_validation())
