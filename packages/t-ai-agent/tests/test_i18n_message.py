import asyncio

from t_ai_agent.message_i18n import get_i18n_message
from t_ai_app.ctx import ReqCtx


async def test_model_json_schema():
    content_text = get_i18n_message(
        "as_tool_handoff_content",
        ReqCtx.get_lang(),
        target_agent_name='abc'
    )
    print(content_text)

    content_text = get_i18n_message(
        "tool_name_knowledge_base_search",
        ReqCtx.get_lang()
    )
    print(content_text)


if __name__ == "__main__":
    asyncio.run(test_model_json_schema())
