"""数据集迁移

Revision ID: 66f60d499d89
Revises: 9d8966f60d49
Create Date: 2025-08-13 11:34:20.957357+00:00

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql
import json
from typing import Dict, Any, List, Optional

# revision identifiers, used by Alembic.
revision = "66f60d499d89"
down_revision = "9d8966f60d49"
branch_labels = None
depends_on = None


def upgrade():
    """升级：迁移历史数据到新的简化结构"""

    # 获取数据库连接
    connection = op.get_bind()

    # 1. 迁移 dataset_items 表的数据
    migrate_dataset_items(connection)

    # 2. 添加新的字段（如果需要）
    # 注意：由于我们使用 Text 类型存储 JSON，实际上不需要修改表结构
    # 只需要迁移数据内容即可


def downgrade():
    """降级：恢复到旧的数据结构（如果需要）"""

    # 获取数据库连接
    connection = op.get_bind()

    # 恢复旧的数据结构
    restore_old_structure(connection)


def migrate_dataset_items(connection):
    """迁移数据集项目数据"""

    print("开始迁移 dataset_items 表...")

    # 查询所有数据集项目
    result = connection.execute(sa.text("""
        SELECT id, input_data, expected_output, meta_info 
        FROM dataset_items 
        WHERE input_data IS NOT NULL
    """))

    migrated_count = 0
    error_count = 0

    for row in result:
        try:
            item_id = row[0]
            input_data = row[1]
            expected_output = row[2]
            meta_info = row[3]

            # 迁移单个项目
            new_input_data = migrate_single_item(input_data, expected_output)

            # 更新数据库
            connection.execute(sa.text("""
                UPDATE dataset_items 
                SET input_data = :input_data, 
                    expected_output = NULL,
                    updated_at = NOW()
                WHERE id = :id
            """), {
                'input_data': new_input_data,
                'id': item_id
            })

            migrated_count += 1
            print(f"✅ 成功迁移项目 {item_id}")

        except Exception as e:
            error_count += 1
            print(f"❌ 迁移项目 {row[0]} 失败: {e}")

    print(f"迁移完成！成功: {migrated_count}, 失败: {error_count}")


def migrate_single_item(input_data: Any, expected_output: Any) -> str:
    """迁移单个数据集项目的数据结构 - 将所有数据转换为多轮格式"""

    # 如果 input_data 已经是新的多轮格式，直接返回
    if isinstance(input_data, str):
        try:
            parsed = json.loads(input_data)
            if isinstance(parsed, list) and len(parsed) > 0:
                # 检查是否已经是新格式
                if isinstance(parsed[0], dict) and 'question' in parsed[0]:
                    return input_data  # 已经是新格式
        except:
            pass

    # 处理旧格式的数据
    if isinstance(input_data, str):
        # 尝试解析 JSON
        try:
            parsed_input = json.loads(input_data)
        except:
            # 如果不是 JSON，将单轮问题转换为多轮格式
            return json.dumps([{
                'question': input_data,
                'expectedAnswer': expected_output or ''
            }], ensure_ascii=False)

    elif isinstance(input_data, dict):
        parsed_input = input_data
    else:
        parsed_input = input_data

    # 检查是否为旧的多轮格式
    if isinstance(parsed_input, dict) and 'turns' in parsed_input:
        # 旧格式：{"turns": [{"question": "...", "expectedAnswer": "..."}]}
        turns = parsed_input['turns']
        new_turns = []

        for turn in turns:
            if isinstance(turn, dict):
                question = turn.get('question', '') or turn.get('text', '') or turn.get('user', '')
                expected_answer = turn.get('expectedAnswer', '') or turn.get('answer', '')

                if question:
                    new_turns.append({
                        'question': question,
                        'expectedAnswer': expected_answer
                    })

        if new_turns:
            return json.dumps(new_turns, ensure_ascii=False)

    # 检查是否为单轮格式
    if isinstance(parsed_input, str):
        # 单轮问题，转换为多轮格式
        return json.dumps([{
            'question': parsed_input,
            'expectedAnswer': expected_output or ''
        }], ensure_ascii=False)

    # 如果无法识别格式，转换为单轮多轮格式
    return json.dumps([{
        'question': str(parsed_input),
        'expectedAnswer': expected_output or ''
    }], ensure_ascii=False)


def restore_old_structure(connection):
    """恢复到旧的数据结构（降级时使用）"""

    print("开始恢复到旧的数据结构...")

    # 这里可以实现降级逻辑
    # 由于我们主要是数据迁移，降级可能比较复杂
    # 建议在降级前备份数据

    print("⚠️  注意：降级操作可能丢失数据，建议手动处理")
