"""修复评估结果数据迁移

Revision ID: fix_evaluation_migration
Revises: 1755597598
Create Date: 2025-08-19 21:00:00.000000+00:00

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "1755597598"
down_revision = "66f60d499d89"
branch_labels = None
depends_on = None




def upgrade():
    # 拆分删除旧存储过程为单独的执行语句
    op.execute("DROP PROCEDURE IF EXISTS ConvertEvaluationResult;")
    op.execute("DROP PROCEDURE IF EXISTS BatchConvertEvaluationResults;")

    # 创建单个转换存储过程
    op.execute("""
    CREATE PROCEDURE ConvertEvaluationResult(IN task_id INT)
    BEGIN
        DECLARE done INT DEFAULT FALSE;
        DECLARE i INT DEFAULT 0;
        DECLARE arr_length INT;
        DECLARE original_json JSON;
        DECLARE eval_result JSON;
        DECLARE new_eval_results JSON DEFAULT JSON_ARRAY();

        -- raw字段相关变量
        DECLARE cachedTokens INT;
        DECLARE content TEXT CHARACTER SET utf8mb4;
        DECLARE costTime FLOAT;
        DECLARE inputTokens INT;
        DECLARE outputTokens INT;
        DECLARE reasoningTokens INT;
        DECLARE totalTokens INT;

        -- 其他字段变量
        DECLARE input_val TEXT CHARACTER SET utf8mb4;
        DECLARE output_val TEXT CHARACTER SET utf8mb4;
        DECLARE expected_output_val TEXT CHARACTER SET utf8mb4;
        DECLARE evaluation_val JSON;
        DECLARE raw_val JSON;
        DECLARE evaluation_with_turn JSON;

        SET NAMES utf8mb4;

        -- 获取原始JSON数据
        SELECT result INTO original_json FROM evaluation_tasks WHERE id = task_id;
        SET arr_length = JSON_LENGTH(original_json, '$.evaluationResults');

        WHILE i < arr_length DO
            -- 提取当前evaluationResults元素
            SET eval_result = JSON_EXTRACT(original_json, CONCAT('$.evaluationResults[', i, ']'));

            -- 提取基础字段
            SET input_val = JSON_UNQUOTE(JSON_EXTRACT(eval_result, '$.input'));
            SET output_val = JSON_UNQUOTE(JSON_EXTRACT(eval_result, '$.output'));
            SET expected_output_val = JSON_UNQUOTE(JSON_EXTRACT(eval_result, '$.expectedOutput'));
            SET evaluation_val = JSON_EXTRACT(eval_result, '$.evaluation');

            -- 提取并构造raw对象
            SET cachedTokens = IFNULL(JSON_EXTRACT(eval_result, '$.cachedTokens'), 0);
            SET content = JSON_UNQUOTE(JSON_EXTRACT(eval_result, '$.content'));
            SET costTime = IFNULL(JSON_EXTRACT(eval_result, '$.costTime'), 0);
            SET inputTokens = IFNULL(JSON_EXTRACT(eval_result, '$.inputTokens'), 0);
            SET outputTokens = IFNULL(JSON_EXTRACT(eval_result, '$.outputTokens'), 0);
            SET reasoningTokens = IFNULL(JSON_EXTRACT(eval_result, '$.reasoningTokens'), 0);
            SET totalTokens = IFNULL(JSON_EXTRACT(eval_result, '$.totalTokens'), 0);

            SET raw_val = JSON_OBJECT(
                'cachedTokens', cachedTokens,
                'content', content,
                'costTime', costTime,
                'inputTokens', inputTokens,
                'outputTokens', outputTokens,
                'reasoningTokens', reasoningTokens,
                'totalTokens', totalTokens
            );

            -- 为evaluation对象添加turn:1字段
            SET evaluation_with_turn = JSON_MERGE_PATCH(
                evaluation_val,
                JSON_OBJECT('turn', 1)
            );

            -- 构造完整的evaluationResults元素
            SET eval_result = JSON_MERGE(
                JSON_REMOVE(eval_result, 
                    '$.input', '$.output', '$.expectedOutput', '$.evaluation',
                    '$.cachedTokens', '$.content', '$.costTime', '$.inputTokens',
                    '$.outputTokens', '$.reasoningTokens', '$.totalTokens'
                ),
                JSON_OBJECT(
                    'turns', JSON_ARRAY(
                        JSON_OBJECT(
                            'turn', 1,
                            'input', input_val,
                            'output', output_val,
                            'expectedAnswer', expected_output_val,
                            'raw', raw_val
                        )
                    ),
                    'evaluations', JSON_ARRAY(evaluation_with_turn)
                )
            );

            -- 添加到新的evaluationResults数组
            SET new_eval_results = JSON_MERGE(new_eval_results, CONCAT('[', eval_result, ']'));
            SET i = i + 1;
        END WHILE;

        -- 更新记录
        UPDATE evaluation_tasks
        SET result = JSON_MERGE(
            JSON_REMOVE(original_json, '$.evaluationResults'),
            JSON_OBJECT('evaluationResults', new_eval_results)
        )
        WHERE id = task_id;
    END;
    """)

    # 创建批量处理存储过程
    op.execute("""
    CREATE PROCEDURE BatchConvertEvaluationResults()
    BEGIN
        DECLARE done INT DEFAULT FALSE;
        DECLARE task_id INT;
        DECLARE cur CURSOR FOR 
            SELECT id FROM evaluation_tasks 
            WHERE JSON_EXTRACT(result, '$.evaluationResults[0].evaluations[0].turn') IS NULL;
        DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;

        OPEN cur;
        read_loop: LOOP
            FETCH cur INTO task_id;
            IF done THEN
                LEAVE read_loop;
            END IF;
            CALL ConvertEvaluationResult(task_id);
        END LOOP;
        CLOSE cur;
    END;
    """)

    # 执行批量转换
    op.execute("CALL BatchConvertEvaluationResults();")


def downgrade():
    # 降级操作：删除创建的存储过程
    op.execute("DROP PROCEDURE IF EXISTS ConvertEvaluationResult;")
    op.execute("DROP PROCEDURE IF EXISTS BatchConvertEvaluationResults;")