from typing import Any, Dict, List, Optional

from loguru import logger
from sqlalchemy.orm import Session
from t_ai_agent_ops.model.common import PaginatedResponse, PaginationParams
from t_ai_agent_ops.model.crud import DatasetCRUD, DatasetItemCRUD
from t_ai_agent_ops.model.dataset import DatasetCreate, DatasetItemCreate, DatasetItemUpdate, DatasetUpdate
from t_ai_agent_ops.model.db_config import get_db
from t_ai_agent_ops.utils.req_ctx_helper import get_current_team_id, get_current_user_id, get_current_user_name
import json


class DatasetService:
    def __init__(self):
        pass

    def _serialize_field(self, value: Any) -> Optional[str]:
        """将任意值序列化为可存储到 Text 列的字符串。"""
        if value is None:
            return None
        if isinstance(value, (dict, list)):
            try:
                return json.dumps(value, ensure_ascii=False)
            except Exception as e:
                logger.warning(f"JSON序列化失败: {e}, 值: {value}")
                return str(value)
        # 处理ConversationTurn对象或其他有特殊属性的对象
        if hasattr(value, '__dict__'):
            try:
                # 尝试将对象转换为字典
                if hasattr(value, 'dict'):
                    return json.dumps(value.dict(), ensure_ascii=False)
                elif hasattr(value, 'model_dump'):
                    return json.dumps(value.model_dump(), ensure_ascii=False)
                else:
                    # 使用__dict__属性
                    return json.dumps(value.__dict__, ensure_ascii=False)
            except Exception as e:
                logger.warning(f"对象序列化失败: {e}, 值: {value}")
                return str(value)
        return str(value)

    def _deserialize_field(self, value: Any) -> Any:
        """将存储字符串尝试解析回原始结构。"""
        if value is None:
            return None
        if isinstance(value, (dict, list)):
            return value
        if isinstance(value, str):
            text = value.strip()
            if not text:
                return value
            # 尝试解析JSON字符串
            if (text.startswith('{') and text.endswith('}')) or (text.startswith('[') and text.endswith(']')):
                try:
                    parsed = json.loads(text)
                    # 确保解析后的数据是列表或字典
                    if isinstance(parsed, (list, dict)):
                        return parsed
                    else:
                        # 如果不是预期的类型，返回原始值
                        return value
                except (json.JSONDecodeError, TypeError) as e:
                    logger.warning(f"JSON解析失败: {e}, 原始值: {value[:100]}...")
                    return value
            else:
                # 不是JSON格式的字符串，直接返回
                return value
        return value

    def _convert_conversation_turns_to_dict(self, turns: List[Any]) -> List[Dict[str, Any]]:
        """将ConversationTurn对象转换为期望的字典格式"""
        if not turns:
            return []
        
        # 确保turns是列表类型
        if not isinstance(turns, list):
            logger.warning(f"turns不是列表类型: {type(turns)}, 值: {turns}")
            return []
        
        converted_turns = []
        for i, turn in enumerate(turns):
            try:
                if hasattr(turn, 'question') and hasattr(turn, 'expected_answer'):
                    # 如果是ConversationTurn对象，转换为字典
                    converted_turns.append({
                        "question": turn.question,
                        "expectedAnswer": turn.expected_answer
                    })
                elif isinstance(turn, dict):
                    # 如果已经是字典，确保字段名正确
                    question = turn.get("question", "")
                    expected_answer = turn.get("expectedAnswer", turn.get("expected_answer", ""))
                    
                    converted_turn = {
                        "question": question,
                        "expectedAnswer": expected_answer
                    }
                    converted_turns.append(converted_turn)
                else:
                    # 其他情况，记录警告并尝试转换为字符串
                    logger.warning(f"未知的turn类型: {type(turn)}, 值: {turn}")
                    converted_turns.append({
                        "question": str(turn),
                        "expectedAnswer": None
                    })
            except Exception as e:
                logger.error(f"转换第{i}个turn时出错: {e}, turn值: {turn}")
                # 出错时添加一个默认的turn
                converted_turns.append({
                    "question": f"转换错误: {str(turn)[:50]}...",
                    "expectedAnswer": None
                })
        
        logger.debug(f"成功转换 {len(converted_turns)} 个对话轮次")
        return converted_turns

    def _validate_conversation_format(self, input_data: Any) -> bool:
        """验证是否为有效的对话格式"""
        if not isinstance(input_data, list):
            return False
        if len(input_data) == 0:
            return False
        return all(
            isinstance(turn, dict) and 'question' in turn 
            for turn in input_data
        )

    async def create_dataset(self, dataset_create: DatasetCreate) -> Dict[str, Any]:
        """创建数据集"""
        db = next(get_db())
        try:
            # 从 ReqCtx 获取当前用户和团队信息
            current_user_id = get_current_user_id()
            current_user_name = get_current_user_name()
            current_team_id = get_current_team_id()

            # 创建数据集
            dataset = DatasetCRUD.create_dataset(
                db=db,
                name=dataset_create.name,
                agent_key=dataset_create.agent_key,
                description=dataset_create.description,
                meta_info=dataset_create.metadata,
                created_by=current_user_id or current_user_name or dataset_create.created_by,
                team_id=current_team_id or dataset_create.team_id,
            )

            # 如果提供了项目列表，则创建数据集项目
            if dataset_create.items:
                for item in dataset_create.items:
                    # 将ConversationTurn对象转换为字典格式，然后序列化
                    input_data = self._convert_conversation_turns_to_dict(item.input)
                    DatasetItemCRUD.create_item(
                        db=db,
                        dataset_id=dataset.id,
                        input_data=self._serialize_field(input_data),
                        expected_output=None,  # 新格式不需要这个字段
                        meta_info=item.metadata,
                        created_by=current_user_id or current_user_name or item.created_by,
                        team_id=current_team_id or item.team_id,
                    )

            return {
                "id": dataset.id,
                "name": dataset.name,
                "description": dataset.description,
                "agentKey": dataset.agent_key,
                "metaInfo": dataset.meta_info,
                "createdAt": dataset.created_at.isoformat(),
                "updatedAt": dataset.updated_at.isoformat(),
                "createdBy": dataset.created_by,
                "updatedBy": dataset.updated_by,
                "teamId": dataset.team_id,
                "version": dataset.version,
            }
        finally:
            db.close()

    async def get_dataset(self, dataset_id: int) -> Optional[Dict[str, Any]]:
        """获取数据集详情"""
        db = next(get_db())
        try:
            dataset = DatasetCRUD.get_dataset(db, dataset_id)
            if not dataset:
                return None

            # 获取数据集的所有 items
            items = DatasetItemCRUD.get_items(db, dataset.id)

            return {
                "id": dataset.id,
                "name": dataset.name,
                "description": dataset.description,
                "agentKey": dataset.agent_key,
                "metaInfo": dataset.meta_info,
                "createdAt": dataset.created_at.isoformat(),
                "updatedAt": dataset.updated_at.isoformat(),
                "createdBy": dataset.created_by,
                "updatedBy": dataset.updated_by,
                "teamId": dataset.team_id,
                "version": dataset.version,
                "items": [
                    {
                        "id": item.id,
                        "datasetId": item.dataset_id,
                        "input": self._convert_conversation_turns_to_dict(self._deserialize_field(item.input_data)),
                        "expectedOutput": None,  # 新格式不需要这个字段
                        "metaInfo": self._deserialize_field(item.meta_info),
                        "createdAt": item.created_at.isoformat(),
                        "updatedAt": item.updated_at.isoformat(),
                        "createdBy": item.created_by,
                        "updatedBy": item.updated_by,
                        "teamId": item.team_id,
                        "version": item.version,
                    }
                    for item in items
                ],
            }
        finally:
            db.close()

    async def list_datasets(
        self, pagination: PaginationParams, agent_key: Optional[str] = None
    ) -> PaginatedResponse[Dict[str, Any]]:
        """获取数据集分页列表
        Args:
            pagination: 分页参数
            agent_key: 按 agent key 过滤
        """
        db = next(get_db())
        try:
            # 从 ReqCtx 获取当前团队ID
            current_team_id = get_current_team_id()

            # 获取总数
            total = DatasetCRUD.get_datasets_count(db, agent_key=agent_key, team_id=current_team_id)

            # 获取数据，支持排序
            datasets = DatasetCRUD.get_datasets(
                db,
                skip=pagination.skip,
                limit=pagination.limit,
                agent_key=agent_key,
                team_id=current_team_id,
                sort_by=pagination.sortBy,
                sort_order=pagination.sortOrder,
            )

            # 转换为字典格式
            data_items = [
                {
                    "id": dataset.id,
                    "name": dataset.name,
                    "description": dataset.description,
                    "agentKey": dataset.agent_key,
                    "metaInfo": dataset.meta_info,
                    "createdAt": dataset.created_at.isoformat(),
                    "updatedAt": dataset.updated_at.isoformat(),
                    "createdBy": dataset.created_by,
                    "updatedBy": dataset.updated_by,
                    "teamId": dataset.team_id,
                    "version": dataset.version,
                }
                for dataset in datasets
            ]

            return PaginatedResponse.create(data_items, total, pagination.pageNumber, pagination.pageSize)
        finally:
            db.close()

    async def update_dataset(self, dataset_id: int, dataset_update: DatasetUpdate) -> Optional[Dict[str, Any]]:
        """更新数据集"""
        db = next(get_db())
        try:
            # 从 ReqCtx 获取当前用户和团队信息
            current_user_id = get_current_user_id()
            current_user_name = get_current_user_name()
            current_team_id = get_current_team_id()

            updated_dataset = DatasetCRUD.update_dataset(
                db=db,
                dataset_id=dataset_id,
                name=dataset_update.name,
                description=dataset_update.description,
                agent_key=dataset_update.agent_key,
                meta_info=dataset_update.metadata,
                updated_by=current_user_id or current_user_name or dataset_update.updated_by,
                team_id=current_team_id or dataset_update.team_id,
            )

            if not updated_dataset:
                return None

            return {
                "id": updated_dataset.id,
                "name": updated_dataset.name,
                "description": updated_dataset.description,
                "agentKey": updated_dataset.agent_key,
                "metaInfo": updated_dataset.meta_info,
                "createdAt": updated_dataset.created_at.isoformat(),
                "updatedAt": updated_dataset.updated_at.isoformat(),
                "createdBy": updated_dataset.created_by,
                "updatedBy": updated_dataset.updated_by,
                "teamId": updated_dataset.team_id,
                "version": updated_dataset.version,
            }
        finally:
            db.close()

    async def delete_dataset(self, dataset_id: int) -> bool:
        """删除数据集"""
        db = next(get_db())
        try:
            return DatasetCRUD.delete_dataset(db, dataset_id)
        except Exception as e:
            logger.error(f"Error deleting dataset {dataset_id}: {str(e)}")
            return False

    async def create_dataset_item(self, dataset_id: int, item_create: DatasetItemCreate) -> Optional[Dict[str, Any]]:
        """创建数据集项目"""
        db = next(get_db())
        try:
            dataset = DatasetCRUD.get_dataset(db, dataset_id)
            if not dataset:
                return None

            # 从 ReqCtx 获取当前用户和团队信息
            current_user_id = get_current_user_id()
            current_user_name = get_current_user_name()
            current_team_id = get_current_team_id()

            item = DatasetItemCRUD.create_item(
                db=db,
                dataset_id=dataset.id,
                input_data=self._serialize_field(self._convert_conversation_turns_to_dict(item_create.input)),
                expected_output=None,  # 新格式不需要这个字段
                meta_info=item_create.metadata,
                created_by=current_user_id or current_user_name or item_create.created_by,
                team_id=current_team_id or item_create.team_id,
            )

            return {
                "id": item.id,
                "datasetId": item.dataset_id,
                "input": self._convert_conversation_turns_to_dict(self._deserialize_field(item.input_data)),
                "expectedOutput": None,  # 新格式不需要这个字段
                "metaInfo": self._deserialize_field(item.meta_info),
                "createdAt": item.created_at.isoformat(),
                "updatedAt": item.updated_at.isoformat(),
                "createdBy": item.created_by,
                "updatedBy": item.updated_by,
                "teamId": item.team_id,
                "version": item.version,
            }
        finally:
            db.close()

    async def get_dataset_items(self, dataset_id: int, skip: int = 0, limit: int = 100) -> List[Dict[str, Any]]:
        """获取数据集项目列表"""
        db = next(get_db())
        try:
            dataset = DatasetCRUD.get_dataset_by_id(db, dataset_id)
            if not dataset:
                return []

            items = DatasetItemCRUD.get_items(db, dataset.id, skip=skip, limit=limit)
            return [
                {
                    "id": item.id,
                    "datasetId": item.dataset_id,
                    "input": self._convert_conversation_turns_to_dict(self._deserialize_field(item.input_data)),
                    "expectedOutput": None,  # 新格式不需要这个字段
                    "metaInfo": self._deserialize_field(item.meta_info),
                    "createdAt": item.created_at.isoformat(),
                    "updatedAt": item.updated_at.isoformat(),
                    "createdBy": item.created_by,
                    "updatedBy": item.updated_by,
                    "teamId": item.team_id,
                    "version": item.version,
                }
                for item in items
            ]
        finally:
            db.close()

    async def update_dataset_item(
        self, dataset_id: int, item_id: int, item_update: DatasetItemUpdate
    ) -> Optional[Dict[str, Any]]:
        """更新数据集项目"""
        db = next(get_db())
        try:
            # 验证数据集是否存在
            dataset = DatasetCRUD.get_dataset(db, dataset_id)
            if not dataset:
                return None

            # 从 ReqCtx 获取当前用户和团队信息
            current_user_id = get_current_user_id()
            current_user_name = get_current_user_name()
            current_team_id = get_current_team_id()

            # 更新数据集项目
            item = DatasetItemCRUD.update_item(
                db=db,
                item_id=item_id,
                input_data=self._serialize_field(self._convert_conversation_turns_to_dict(item_update.input)) if item_update.input else None,
                expected_output=None,  # 新格式不需要这个字段
                meta_info=item_update.metadata,
                updated_by=current_user_id or current_user_name or item_update.updated_by,
                team_id=current_team_id or item_update.team_id,
            )

            if not item:
                return None

            return {
                "id": item.id,
                "datasetId": item.dataset_id,
                "input": self._convert_conversation_turns_to_dict(self._deserialize_field(item.input_data)),
                "expectedOutput": None,  # 新格式不需要这个字段
                "metaInfo": self._deserialize_field(item.meta_info),
                "createdAt": item.created_at.isoformat(),
                "updatedAt": item.updated_at.isoformat(),
                "createdBy": item.created_by,
                "updatedBy": item.updated_by,
                "teamId": item.team_id,
                "version": item.version,
            }
        finally:
            db.close()
