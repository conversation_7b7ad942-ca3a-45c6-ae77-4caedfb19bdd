import json
import time
from typing import Optional, Dict, Any, List
from dataclasses import dataclass
from loguru import logger

@dataclass
class ClientDetails:
    """客户端详细信息数据类"""
    client: Dict[str, Any]
    models: List[Dict[str, Any]]

@dataclass
class ModelPricing:
    """模型价格信息数据类"""
    model_id: str
    model_name: str
    provider_name: str
    input_token_price: float
    output_token_price: float
    unit: str
    context_length: int
    max_completion_tokens: int
    max_prompt_tokens: int

class AIProxyService:
    """AI代理REST处理器"""
    
    def __init__(self):
        self.ai_proxy_domain = "https://ai-proxy.erda.cloud"
        self.access_key_id = "f85e417827fc473ea3a4c1156c288649"
        self.api_prefix = "api/ai-proxy"
        self.url_prefix = f"{self.ai_proxy_domain}/{self.api_prefix}"
        self.headers = {
            "Authorization": f"Bearer {self.access_key_id}"
        }
        # 缓存过期时间：2小时 = 7200秒
        self.cache_ttl = 7200
        self._last_cache_time = time.time()  # 初始化为当前时间，表示缓存未过期
        self._cached_model_pricing = None

    def _is_cache_expired(self) -> bool:
        """检查缓存是否过期"""
        return time.time() - self._last_cache_time > self.cache_ttl

    def get_client_details(self) -> Optional[ClientDetails]:
        """
        获取客户端详细信息
        
        Returns:
            ClientDetails: 客户端详细信息，如果请求失败则返回None
        """
        url = f"{self.url_prefix}/clients/actions/get-by-access-key-id"
        
        try:
            # 发送HTTP GET请求
            import httpx
            with httpx.Client(timeout=30) as client:
                response = client.get(url, headers=self.headers)
                response.raise_for_status()  # 检查HTTP状态码
                
                result = response.text
            
            # 验证响应是否为有效的JSON
            try:
                json_data = json.loads(result)
            except json.JSONDecodeError:
                logger.error("invoke ai-proxy request client details error, response is not json")
                return None
            
            # 检查响应结构
            if "data" in json_data and "success" in json_data:
                if json_data["success"]:
                    logger.info(f"client details response:\n{json.dumps(json_data['data'], indent=2)}")
                    # 更新缓存时间
                    self._last_cache_time = time.time()
                    return ClientDetails(**json_data["data"])
                else:
                    # 处理错误响应
                    error_message = json_data.get("err", {}).get("suggest", "")
                    if not error_message:
                        error_message = json_data.get("errMsg", "Unknown error")
                    logger.error(f"invoke ai-proxy request client details error: {error_message}")
            else:
                logger.error("invoke ai-proxy request client details error, invalid response structure")
                
        except ImportError:
            logger.error("httpx library not available, please install it")
        except Exception as e:
            logger.error(f"invoke ai-proxy request client details exception: {e}")
        
        return None

    def get_model_pricing(self) -> Optional[List[ModelPricing]]:
        """
        获取模型价格信息，包含2小时TTL缓存
        
        Returns:
            List[ModelPricing]: 模型价格信息列表，如果请求失败则返回None
        """
        # 检查缓存是否过期且缓存数据存在
        if not self._is_cache_expired() and self._cached_model_pricing is not None:
            logger.debug("Using cached model pricing data")
            return self._cached_model_pricing
        
        logger.info("缓存过期或不存在，重新获取模型价格数据")
        # 缓存过期或不存在，重新获取数据
        client_details = self.get_client_details()
        if not client_details or not client_details.models:
            logger.warning("Failed to get client details or no models available")
            return None
        
        model_pricing_list = []
        
        for model_info in client_details.models:
            try:
                model_data = model_info.get("model", {})
                provider_data = model_info.get("provider", {})
                metadata = model_data.get("metadata", {}).get("public", {})
                pricing = metadata.get("pricing", {})
                context = metadata.get("context", {})
                
                logger.debug(f"解析模型信息: {model_data.get('name', 'unknown')} - 提供商: {provider_data.get('name', 'unknown')}")
                logger.debug(f"价格信息: {pricing}")
                logger.debug(f"上下文信息: {context}")
                
                # 提取价格信息
                input_token_price = float(pricing.get("prompt", "0"))
                output_token_price = float(pricing.get("completion", "0"))
                unit = pricing.get("unit", "RMB")
                
                logger.debug(f"提取的价格 - 输入: {input_token_price} {unit}/1K tokens, 输出: {output_token_price} {unit}/1K tokens")
                
                # 创建模型价格对象
                model_pricing = ModelPricing(
                    model_id=model_data.get("id", ""),
                    model_name=model_data.get("name", ""),
                    provider_name=provider_data.get("name", ""),
                    input_token_price=input_token_price,
                    output_token_price=output_token_price,
                    unit=unit,
                    context_length=context.get("context_length", 0),
                    max_completion_tokens=context.get("max_completion_tokens", 0),
                    max_prompt_tokens=context.get("max_prompt_tokens", 0)
                )
                
                model_pricing_list.append(model_pricing)
                logger.debug(f"成功添加模型价格信息: {model_pricing}")
                
            except (ValueError, KeyError, TypeError) as e:
                logger.warning(f"Failed to parse model pricing for model {model_data.get('name', 'unknown')}: {e}")
                continue
        
        # 更新缓存
        self._cached_model_pricing = model_pricing_list
        self._last_cache_time = time.time()
        
        logger.info(f"Successfully extracted pricing for {len(model_pricing_list)} models")
        return model_pricing_list

    def get_model_pricing_dict(self) -> Optional[Dict[str, ModelPricing]]:
        """
        获取模型价格信息字典，以模型ID为键
        
        Returns:
            Dict[str, ModelPricing]: 模型价格信息字典，如果请求失败则返回None
        """
        model_pricing_list = self.get_model_pricing()
        if not model_pricing_list:
            return None
        
        return {model.model_id: model for model in model_pricing_list}

    def get_model_pricing_by_name(self, model_name: str) -> Optional[ModelPricing]:
        """
        根据模型名称获取价格信息
        
        Args:
            model_name: 模型名称
            
        Returns:
            ModelPricing: 模型价格信息，如果未找到则返回None
        """
        logger.info(f"尝试获取模型 {model_name} 的价格信息")
        model_pricing_list = self.get_model_pricing()
        if not model_pricing_list:
            logger.warning(f"无法获取模型价格列表")
            return None
        
        logger.info(f"当前可用的模型数量: {len(model_pricing_list)}")
        available_models = [f"{p.model_name}({p.provider_name})" for p in model_pricing_list]
        logger.info(f"可用模型列表: {available_models}")
        
        for model in model_pricing_list:
            if model.model_name == model_name:
                logger.info(f"找到匹配的模型: {model}")
                return model
        
        logger.warning(f"未找到模型 {model_name} 的价格信息")
        return None

# 使用示例
def main():
    # 创建处理器实例
    handler = AIProxyService()
    
    # 获取客户端详情
    client_details = handler.get_client_details()
    
    if client_details:
        print("成功获取客户端详情:")
        print(f"客户端信息: {client_details.client}")
        print(f"模型数量: {len(client_details.models)}")
        
        # 获取模型价格信息
        model_pricing = handler.get_model_pricing()
        if model_pricing:
            print("\n模型价格信息:")
            for model in model_pricing:
                print(f"模型: {model.model_name}")
                print(f"  输入Token价格: {model.input_token_price} {model.unit}")
                print(f"  输出Token价格: {model.output_token_price} {model.unit}")
                print(f"  上下文长度: {model.context_length}")
                print(f"  提供商: {model.provider_name}")
                print()
        
        # 获取特定模型的价格
        specific_model = handler.get_model_pricing_by_name("deepseek-v3-250324")
        if specific_model:
            print(f"特定模型价格信息:")
            print(f"模型: {specific_model.model_name}")
            print(f"输入Token价格: {specific_model.input_token_price} {specific_model.unit}/tokens")
            print(f"输出Token价格: {specific_model.output_token_price} {specific_model.unit}/tokens")
    else:
        print("获取客户端详情失败")

if __name__ == "__main__":
    main()