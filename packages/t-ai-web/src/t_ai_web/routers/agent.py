from fastapi import APIRouter, Request
from t_ai_agent import agent_service
from t_ai_agent.sandbox import sandbox_service
from t_ai_agent.agent_warmup import update_agent_cache

agent_router = APIRouter(prefix="/agent")


@agent_router.get("/index")
async def agent():
    return "Agent client is running."


@agent_router.post("/run")
async def run(request: Request):
    return await agent_service.run(await request.json())


@agent_router.post("/run_streamed")
async def run_streamed(request: Request):
    return await agent_service.run_streamed(await request.json())


@agent_router.post("/run_sandbox")
async def run_streamed(request: Request):
    return await sandbox_service.run_sandbox(await request.json())


@agent_router.post("/cache/update")
async def update_cache(request: Request):
    agent_request = await request.json()
    return await update_agent_cache(agent_request.get("agent"))
