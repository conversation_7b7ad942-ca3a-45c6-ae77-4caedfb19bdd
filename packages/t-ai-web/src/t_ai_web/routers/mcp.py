import json

from fastapi import APIRouter, Request
from loguru import logger

# from t_ai_agent.mcp_agent.agent_compatible_mcp_client import AgentCompatibleMCPClient
from t_ai_app import G
from t_ai_app.constants import TRACE_ID_HEADER_NAME
from t_ai_app.ctx import ReqCtx
from t_ai_mcp import TMCPHelper, TMCPServer, TMCPServerConfig

mcp_router = APIRouter(prefix="/mcp")


@mcp_router.post("/run")
async def run(request: Request):
    mcp_request = await request.json()
    mcp_server_name = mcp_request.get("server_name")
    mcp_server_version = mcp_request.get("server_version")
    mcp_server_args = mcp_request.get("server_args", {})
    tool_name = mcp_request.get("tool_name")
    tool_args = mcp_request.get("tool_args")
    mcp_server_info = await G.AI_PROXY_ADMIN_CLIENT.get_mcp_server_info(
        mcp_server_name=mcp_server_name, mcp_server_version=mcp_server_version
    )

    # 添加数据大小限制
    MAX_RESPONSE_SIZE = 50 * 1024 * 1024  # 50MB 限制

    if mcp_server_info is not None:
        async with TMCPServer(
            mcp_server_name=mcp_server_name,
            config=TMCPServerConfig(
                server_endpoint=mcp_server_info.endpoint,
                server_args=mcp_server_args,
                # request_id_factory=lambda: ReqCtx.get_trace_id(),
                headers_factory=lambda: {
                    TRACE_ID_HEADER_NAME: ReqCtx.get_trace_id(),
                    "Authorization": G.APP_SETTING.ai_proxy.admin_authorization,
                },
            ),
        ) as mcp_server:
            result = None
            try:
                call_tool_result = await mcp_server.call_tool(tool_name, tool_args)
                result = TMCPHelper.extract_text_from_call_tool_result(call_tool_result)

                # 检查响应大小
                if len(result.encode('utf-8')) > MAX_RESPONSE_SIZE:
                    logger.warning(f"MCP 响应结果太大, size: {len(result)} 字符")

                result = json.loads(result)
            except Exception as e:
                logger.warning(f"mcp tool call response is not json: {e}")
            finally:
                return {
                    "success": True,
                    "data": result,
                }

    else:
        return {
            "success": True,
            "data": None,
        }
