"""
图片生成和编辑路由

基于Azure OpenAI DALL-E API实现图片生成和编辑功能，
生成的图片存储到OSS中。
"""

import base64
import uuid
from datetime import datetime
from typing import Optional, List, Literal

import httpx
from fastapi import APIRouter, HTTPException
from pydantic import BaseModel, Field
from loguru import logger

from t_ai_common.clients import get_oss_client
from t_ai_web.configuration.azure_openai import get_azure_openai_config


# 创建路由器
image_router = APIRouter(prefix="/api/v1/image", tags=["图片生成"])


# 请求和响应模型
class ImageGenerationRequest(BaseModel):
    """图片生成请求模型"""

    prompt: str = Field(..., description="图片生成提示词")
    model: Literal["dall-e-3", "gpt-image-1"] = Field(default="gpt-image-1", description="使用的模型")
    size: Literal["1024x1024", "1024x1792", "1792x1024", "1024x1536", "1536x1024"] = Field(
        default="1024x1024", description="图片尺寸"
    )
    quality: Literal["low", "medium", "high"] = Field(
        default="high", description="图片质量"
    )
    style: Optional[Literal["natural", "vivid"]] = Field(
        default="vivid", description="图片风格（仅DALL-E 3支持）"
    )
    n: int = Field(default=1, ge=1, le=10, description="生成图片数量")
    user: Optional[str] = Field(default=None, description="用户标识")


class ImageEditRequest(BaseModel):
    """图片编辑请求模型"""

    imageUrl: str = Field(..., description="要编辑的图片OSS URL地址")
    prompt: str = Field(..., description="编辑提示词")
    model: Literal["gpt-image-1"] = Field(default="gpt-image-1", description="使用的模型")
    size: Literal["1024x1024", "1024x1536", "1536x1024"] = Field(
        default="1024x1024", description="图片尺寸"
    )
    quality: Literal["low", "medium", "high"] = Field(
        default="high", description="图片质量"
    )
    n: int = Field(default=1, ge=1, le=10, description="生成图片数量")
    user: Optional[str] = Field(default=None, description="用户标识")


class ImageResponse(BaseModel):
    """图片响应模型"""

    url: str = Field(..., description="图片URL")
    key: str = Field(..., description="OSS对象键名")
    created_at: datetime = Field(..., description="创建时间")
    model: str = Field(..., description="使用的模型")
    prompt: str = Field(..., description="使用的提示词")
    size: str = Field(..., description="图片尺寸")
    quality: str = Field(..., description="图片质量")


class ImageGenerationResponse(BaseModel):
    """图片生成响应模型"""

    images: List[ImageResponse] = Field(..., description="生成的图片列表")
    total: int = Field(..., description="生成图片总数")


class ImageService:
    """图片服务类"""

    def __init__(self):
        self.oss_client = get_oss_client()
        self.config = get_azure_openai_config()

    def _get_api_url(self, path: str) -> str:
        """获取API URL"""
        return f"{self.config.endpoint}/openai/deployments/{self.config.deployment_name}/{path}?api-version={self.config.api_version}"

    def _get_headers(self) -> dict:
        """获取请求头"""
        return {
            "Content-Type": "application/json",
            "api-key": self.config.api_key
        }

    def _generate_oss_key(self, filename: str, prefix: str = "images") -> str:
        """生成OSS对象键名"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        unique_id = str(uuid.uuid4())[:8]
        return f"{prefix}/{timestamp}_{unique_id}_{filename}"

    async def generate_image(self, request: ImageGenerationRequest) -> List[ImageResponse]:
        """生成图片"""
        try:
            # 构建请求体
            payload = {
                "prompt": request.prompt,
                "size": request.size,
                "n": request.n
            }

            # 根据模型添加特定参数
            if request.model == "dall-e-3":
                payload["quality"] = request.quality
                if request.style:
                    payload["style"] = request.style
            elif request.model == "gpt-image-1":
                payload["model"] = request.model
                payload["quality"] = request.quality

            if request.user:
                payload["user"] = request.user

            # 调用Azure OpenAI API
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    self._get_api_url("images/generations"),
                    headers=self._get_headers(),
                    json=payload,
                    timeout=120.0
                )

                if response.status_code != 200:
                    logger.error(f"Azure OpenAI API调用失败: {response.status_code} - {response.text}")
                    raise HTTPException(status_code=500, detail="图片生成失败")

                result = response.json()

                # 处理响应
                images = []
                for i, data in enumerate(result.get("data", [])):
                    if "url" in data:
                        # DALL-E 3返回URL
                        image_url = data["url"]
                        # 下载图片并上传到OSS
                        oss_key = await self._download_and_upload_to_oss(image_url, f"generated_{i+1}.png")
                    elif "b64_json" in data:
                        # GPT-image-1返回base64数据
                        image_data = base64.b64decode(data["b64_json"])
                        oss_key = self._upload_base64_to_oss(image_data, f"generated_{i+1}.png")
                    else:
                        continue

                    images.append(ImageResponse(
                        url=self.oss_client.get_object_url(oss_key),
                        key=oss_key,
                        created_at=datetime.fromtimestamp(result.get("created", 0)),
                        model=request.model,
                        prompt=request.prompt,
                        size=request.size,
                        quality=request.quality
                    ))

                return images

        except Exception as e:
            logger.error(f"图片生成失败: {e}")
            raise HTTPException(status_code=500, detail=f"图片生成失败: {str(e)}")

    async def edit_image(self, request: ImageEditRequest) -> List[ImageResponse]:
        """编辑图片"""
        try:
            # 从URL下载图片
            image_data = await self._download_image_from_url(request.imageUrl)

            if len(image_data) > 50 * 1024 * 1024:  # 50MB限制
                raise HTTPException(status_code=400, detail="图片文件大小不能超过50MB")

            # 构建multipart表单数据
            files = {
                "image": ("image.png", image_data, "image/png")
            }

            data = {
                "prompt": request.prompt,
                "model": request.model,
                "size": request.size,
                "n": str(request.n),
                "quality": request.quality
            }

            if request.user:
                data["user"] = request.user

            # 调用Azure OpenAI API
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    self._get_api_url("images/edits"),
                    headers={"api-key": self.config.api_key},
                    files=files,
                    data=data,
                    timeout=120.0
                )

                if response.status_code != 200:
                    logger.error(f"Azure OpenAI API调用失败: {response.status_code} - {response.text}")
                    raise HTTPException(status_code=500, detail="图片编辑失败")

                result = response.json()

                # 处理响应
                images = []
                for i, data in enumerate(result.get("data", [])):
                    if "b64_json" in data:
                        # GPT-image-1返回base64数据
                        image_data = base64.b64decode(data["b64_json"])
                        oss_key = self._upload_base64_to_oss(image_data, f"edited_{i+1}.png")

                        images.append(ImageResponse(
                            url=self.oss_client.get_object_url(oss_key),
                            key=oss_key,
                            created_at=datetime.fromtimestamp(result.get("created", 0)),
                            model=request.model,
                            prompt=request.prompt,
                            size=request.size,
                            quality=request.quality
                        ))

                return images

        except Exception as e:
            logger.error(f"图片编辑失败: {e}")
            raise HTTPException(status_code=500, detail=f"图片编辑失败: {str(e)}")

    async def _download_image_from_url(self, image_url: str) -> bytes:
        """从URL下载图片"""
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(image_url, timeout=30.0)
                response.raise_for_status()
                return response.content
        except Exception as e:
            logger.error(f"从URL下载图片失败: {e}")
            raise HTTPException(status_code=400, detail=f"无法从URL下载图片: {str(e)}")

    async def _download_and_upload_to_oss(self, image_url: str, filename: str) -> str:
        """下载图片并上传到OSS"""
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(image_url, timeout=30.0)
                response.raise_for_status()

                image_data = response.content
                oss_key = self._generate_oss_key(filename)

                self.oss_client.put_object(oss_key, image_data, "image/png")
                return oss_key

        except Exception as e:
            logger.error(f"下载并上传图片失败: {e}")
            raise

    def _upload_base64_to_oss(self, image_data: bytes, filename: str) -> str:
        """上传base64图片数据到OSS"""
        try:
            oss_key = self._generate_oss_key(filename)
            self.oss_client.put_object(oss_key, image_data, "image/png")
            return oss_key

        except Exception as e:
            logger.error(f"上传图片到OSS失败: {e}")
            raise


# 创建服务实例
image_service = ImageService()


# 路由处理函数
@image_router.post("/generate", response_model=ImageGenerationResponse)
async def generate_image(request: ImageGenerationRequest) -> ImageGenerationResponse:
    """
    生成图片

    根据文本提示生成图片，支持DALL-E 3和GPT-image-1模型。
    生成的图片将存储到OSS中。

    Args:
        request: 图片生成请求参数

    Returns:
        ImageGenerationResponse: 生成的图片信息

    Raises:
        HTTPException: 生成失败时抛出异常
    """
    try:
        logger.info(f"开始生成图片，提示词: {request.prompt}")
        images = await image_service.generate_image(request)

        return ImageGenerationResponse(
            images=images,
            total=len(images)
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"图片生成异常: {e}")
        raise HTTPException(status_code=500, detail="图片生成失败")


@image_router.post("/edit", response_model=ImageGenerationResponse)
async def edit_image(request: ImageEditRequest) -> ImageGenerationResponse:
    """
    编辑图片

    根据文本提示编辑指定的图片，支持GPT-image-1模型。
    编辑后的图片将存储到OSS中。

    Args:
        request: 图片编辑请求参数，包含要编辑的图片URL和编辑提示词

    Returns:
        ImageGenerationResponse: 编辑后的图片信息

    Raises:
        HTTPException: 编辑失败时抛出异常
    """
    try:
        logger.info(f"开始编辑图片，提示词: {request.prompt}, 图片URL: {request.imageUrl}")
        images = await image_service.edit_image(request)

        return ImageGenerationResponse(
            images=images,
            total=len(images)
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"图片编辑异常: {e}")
        raise HTTPException(status_code=500, detail="图片编辑失败")


# @image_router.get("/list")
async def list_images(prefix: Optional[str] = None, max_keys: int = 100) -> dict:
    """
    列出OSS中的图片

    Args:
        prefix: 前缀过滤
        max_keys: 最大返回数量

    Returns:
        dict: 图片列表
    """
    try:
        oss_client = get_oss_client()
        keys = oss_client.list_objects(prefix=prefix, max_keys=max_keys)

        images = []
        for key in keys:
            if key.lower().endswith(('.png', '.jpg', '.jpeg', '.gif', '.webp')):
                images.append({
                    "key": key,
                    "url": oss_client.get_object_url(key),
                    "filename": oss_client.get_filename_from_key(key)
                })

        return {
            "images": images,
            "total": len(images)
        }

    except Exception as e:
        logger.error(f"列出图片失败: {e}")
        raise HTTPException(status_code=500, detail="列出图片失败")


@image_router.delete("/{key:path}")
async def delete_image(key: str) -> dict:
    """
    删除OSS中的图片

    Args:
        key: 图片对象键名

    Returns:
        dict: 删除结果
    """
    try:
        logger.info(f"开始删除图片，key: {key}")
        oss_client = get_oss_client()
        success = oss_client.delete_object(key)

        if success:
            return {"message": "图片删除成功", "key": key}
        else:
            raise HTTPException(status_code=500, detail="图片删除失败")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除图片失败: {e}")
        raise HTTPException(status_code=500, detail="删除图片失败")