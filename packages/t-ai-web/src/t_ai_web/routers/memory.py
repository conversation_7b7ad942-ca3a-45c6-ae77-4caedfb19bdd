import re
from typing import List, Optional

from fastapi import APIRouter
from pydantic import BaseModel
from t_ai_agent.utils.attachment_handler import create_client
from t_ai_agent.utils.json_util import safe_json_loads

memory_router = APIRouter(prefix="/agent/memory")


class MemoryVariable(BaseModel):
    name: str
    description: Optional[str] = None
    oldValue: Optional[str] = None


class MemoryFragment(BaseModel):
    id: str
    oldValue: Optional[str] = None


class MemoryExtractAndUpdateRequest(BaseModel):
    model: str
    currentInput: str
    variables: Optional[List[MemoryVariable]] = None
    fragments: Optional[List[MemoryFragment]] = None
    agentDescription: Optional[str] = None


class UpdatedVariable(BaseModel):
    name: str
    newValue: str
    oldValue: Optional[str] = None


class UpdatedFragment(BaseModel):
    id: Optional[str] = None
    text: str | None
    action: str


class MemoryExtractAndUpdateResponse(BaseModel):
    updatedVariables: List[UpdatedVariable]
    updatedFragments: List[UpdatedFragment] = []


@memory_router.post("/extract-and-update", response_model=MemoryExtractAndUpdateResponse)
async def extract_and_update_memory(req: MemoryExtractAndUpdateRequest):
    system_prompt, user_prompt = build_prompt(req)

    llm_response = await call_llm(req.model, system_prompt, user_prompt)

    updated_variables, updated_fragments = parse_llm_response(llm_response)

    updated_fragments = post_process_fragments(updated_fragments, req.fragments or [])

    return MemoryExtractAndUpdateResponse(updatedVariables=updated_variables, updatedFragments=updated_fragments)


SYSTEM_PROMPT_TEMPLATE = """
你是一个极其严谨且智能的逻辑分析与用户偏好总结引擎。

【核心原则】
1.  **信息整合优先**: 除非直接矛盾，否则应整合新旧信息，而不是用新信息替换旧信息，以构建更丰富的用户画像。
2.  如果新信息已被变量更新吸收，则不再新增对应主题的片段。

【偏好信息定义】
偏好信息：用户的持久性特征，如喜好厌恶、工作习惯、业务规则偏好、期望等。
非偏好信息：临时询问、一次性任务、技术咨询等，禁止记录为片段。

【关于变量和片段的说明】
- 记忆变量用于存储用户偏好的总体总结，不可新增，只能更新。
- 记忆片段用于存储具体、分散的偏好描述，可以新增。

【核心信息整合规则】
**以下规则对变量和片段的更新同样适用**：
1.  **具体化处理**: 当新输入是旧信息的具体化时（例如 "蔬菜" -> "黄瓜"），**必须**采用"概括 + 补充"的格式。
    - **格式**: `[旧信息核心], 特别是[新信息]`
    - **示例**: "用户喜欢有氧运动" + "我喜欢慢跑" -> "用户喜欢有氧运动，特别是慢跑"
    - **禁令**: **严禁**用具体信息直接替换概括信息。
2.  **矛盾修正**: 当新输入与旧信息的核心立场冲突时（例如 "喜欢" vs "不喜欢"），以新输入为准，修正旧信息中的矛盾点。
3.  **常规整合**: 在不属于以上两种情况时，在旧信息基础上融合新偏好，保留双方核心信息。

【任务要求】

1.  **变量更新 (updatedVariables)**
    {variable_rule}
    - 判断用户输入是否与【变量描述】高度相关。
    - 如果相关，**遵循【核心信息整合规则】** 生成 newValue。
    - 如果处理后的 newValue 与 oldValue 语义相同，则不返回。

2.  **片段更新 (updatedFragments)**
    - **第一步：关联与更新**
        - 将用户输入与【用户当前记忆片段】的**每一条**进行比对，判断是否存在具体化、矛盾或可整合的关系。
        - **更新 (update)**: 如果存在上述关系，**遵循【核心信息整合规则】** 更新片段内容。
        - **删除 (delete)**: 当用户输入完全推翻片段核心信息时。
        - **忽略**: 如果语义重复，不进行任何操作。
    - **第二步：新增独立信息**
        - 在完成上一步所有变量和片段的更新后，检查用户输入中是否还有**【未被用于任何更新的片段】**。
        - **新增 (add)**: 该信息必须属于偏好信息，否则忽略。

【长度要求】
- 所有 updatedVariables.newValue 与 updatedFragments.text 均需限制在 230 字符以内，如超出请进行信息压缩与概括。

【输出格式要求】
- 严格返回以下 JSON 格式字符串，无任何解释或代码块包装：
{{
  "updatedVariables": [
    {{
      "name": "变量名",
      "newValue": "修改后的值",
      "oldValue": "旧值"
    }}
  ],
  "updatedFragments": [
    {{
      "id": "原 id（新增请返回 null）",
      "text": "片段内容（若为更新则返回修改后的内容，删除则为 null）",
      "action": "add / update / delete"
    }}
  ]
}}

- 如果没有更新，对应数组返回空数组 []。
- 禁止返回任何与输入无关的变量或片段。
- 变量更新请统一主语为"用户"。
"""


def build_prompt(req: MemoryExtractAndUpdateRequest) -> tuple[str, str]:
    variables_str = (
        "\n".join([f"- 变量名: {v.name}，描述: {v.description or ''}，当前值: {v.oldValue}" for v in req.variables or []])
        or "无"
    )

    fragments_str = "\n".join([f"- 片段ID: {f.id}，内容: {f.oldValue}" for f in req.fragments or []]) or "无"

    agent_description_str = req.agentDescription if req.agentDescription else "通用"

    variable_names = [v.name for v in req.variables or []]
    if variable_names:
        variable_names_str = ", ".join(variable_names)
        variable_rule = f"只允许更新如下变量：[{variable_names_str}]，不得凭空生成变量。如无变量定义，updatedVariables 必须返回空数组。"
    else:
        variable_rule = "当前没有任何记忆变量定义，updatedVariables 必须返回空数组，不得凭空生成变量。"

    system_prompt = SYSTEM_PROMPT_TEMPLATE.format(
        variable_rule=variable_rule, agentDescription=agent_description_str
    ).strip()

    user_prompt = f"""
【用户输入】
{req.currentInput}

【用户当前记忆变量】
{variables_str}

【用户当前记忆片段】
{fragments_str}

{agent_description_str}
""".strip()

    return system_prompt, user_prompt


async def call_llm(model: str, system_prompt: str, user_prompt: str) -> str:
    client = await create_client()
    completion = client.chat.completions.create(
        model=model, messages=[{"role": "system", "content": system_prompt}, {"role": "user", "content": user_prompt}]
    )
    return completion.choices[0].message.content


def parse_llm_response(response: str):
    cleaned = re.sub(r"^```json\s*|\s*```$", "", response.strip(), flags=re.MULTILINE)
    data = safe_json_loads(cleaned)
    updated_variables = [
        {"name": item.get("name"), "newValue": item.get("newValue"), "oldValue": item.get("oldValue")}
        for item in data.get("updatedVariables", [])
        if item.get("newValue") != item.get("oldValue")
    ]
    updated_fragments = [
        {"id": item.get("id"), "text": item.get("text"), "action": item.get("action")}
        for item in data.get("updatedFragments", [])
        if item.get("action") != "no_change"
    ]
    return updated_variables, updated_fragments


def post_process_fragments(updated_fragments, old_fragments):
    old_texts = {f.oldValue for f in old_fragments}
    seen_texts = set()
    result = []
    for frag in updated_fragments:
        if frag["action"] == "add" and frag["text"] in old_texts:
            continue
        if frag["action"] in ("add", "update"):
            if frag["text"] is not None and frag["text"] in seen_texts:
                continue
            if frag["text"] is not None:
                seen_texts.add(frag["text"])
        result.append(frag)
    return result
