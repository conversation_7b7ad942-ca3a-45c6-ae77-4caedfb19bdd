"""
Azure OpenAI配置管理

管理Azure OpenAI服务的配置信息，包括资源名称、部署名称、API密钥等。
"""

from pydantic import BaseModel, Field, ConfigDict
from pydantic_settings import BaseSettings


class AzureOpenAISettings(BaseSettings):
    """Azure OpenAI配置设置"""

    model_config = ConfigDict(
        env_file_encoding="utf-8",
        case_sensitive=False,
        extra="allow"
    )

    endpoint: str = Field(
        default="",
        alias="AZURE_OPENAI_ENDPOINT",
        description="Azure OpenAI资源名称"
    )
    deployment_name: str = Field(
        default="",
        alias="AZURE_OPENAI_DEPLOYMENT_NAME",
        description="Azure OpenAI模型部署名称"
    )
    api_key: str = Field(
        default="",
        alias="AZURE_OPENAI_API_KEY",
        description="Azure OpenAI API密钥"
    )
    api_version: str = Field(
        default="2025-04-01-preview",
        alias="AZURE_OPENAI_API_VERSION",
        description="Azure OpenAI API版本"
    )


# 全局配置实例
azure_openai_settings = AzureOpenAISettings()


def get_azure_openai_config() -> AzureOpenAISettings:
    """
    获取Azure OpenAI配置

    Returns:
        AzureOpenAISettings: Azure OpenAI配置实例
    """
    return azure_openai_settings