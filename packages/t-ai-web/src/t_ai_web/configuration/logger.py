import logging
import os
import sys

from loguru import logger
from t_ai_app import G
from t_ai_app.ctx import ReqCtx
from t_ai_app.settings import LogLevel, LogSettings
from loguru import logger
import sys
import os
import logging


def setup_logger():
    # custom_format
    console_format = (
        "<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | "
        "<level>{level: <8}</level> | "
        "trace_id=<cyan>{extra[trace_id]}</cyan> | "
        "<yellow>{name}</yellow>:<yellow>{function}</yellow>:<yellow>{line}</yellow> - "
        "{message}"
    )
    logger.configure(patcher=lambda record: record["extra"].update(trace_id=ReqCtx.get_trace_id()))
    logger.remove()
    log_settings = G.APP_SETTING.log_settings

    logger.add(
        sys.stdout,
        level=log_settings.level,
        format=console_format,
        colorize=True,
        backtrace=log_settings.backtrace,
        diagnose=log_settings.diagnose,
        enqueue=True,  # use background queue to avoid blocking
    )

    # config standard logging module
    _config_standard_logging(log_settings)


class InterceptHandler(logging.Handler):
    def emit(self, record):
        # Get corresponding Loguru level if it exists.
        try:
            level = logger.level(record.levelname).name
        except ValueError:
            level = record.levelno

        # Find caller from where originated the logged message.
        frame, depth = logging.currentframe(), 0
        # skip logging module frames to figure out the actual caller
        while frame and (depth == 0 or frame.f_code.co_filename == logging.__file__):
            frame = frame.f_back
            depth += 1

        logger.opt(depth=depth, exception=record.exc_info).log(level, record.getMessage())


def _config_standard_logging(log_settings: LogSettings):
    """_intercept_standard_logging"""
    # intercept standard logging messages toward Loguru sinks
    logging.basicConfig(handlers=[InterceptHandler()], level=0, force=True)
    if log_settings.level == LogLevel.DEBUG:
        # set root logger level to DEBUG globally
        logging.getLogger().setLevel(logging.DEBUG)
    else:
        # set root logger level to WARNING globally
        logging.getLogger().setLevel(logging.WARNING)
        # for too much noise module like httpx,sse_starlette set to ERROR to avoid too much noise
    logging.getLogger("httpx").setLevel(logging.INFO)
    logging.getLogger("sse_starlette").setLevel(logging.INFO)
        # othres...
