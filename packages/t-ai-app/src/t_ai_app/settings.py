from enum import Enum

from pydantic import Field
from pydantic_settings import BaseSettings


class BaseAppSettings(BaseSettings):
    """可复用的配置基类"""

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False
        extra = "ignore"
        env_ignore_empty = True


class ServerSettings(BaseAppSettings):
    host: str = Field("0.0.0.0", alias="SERVER_HOST")
    port: int = Field(8000, alias="SERVER_PORT")
    reload: bool = Field(False, alias="SERVER_RELOAD")


class DebugSettings(BaseAppSettings):
    enabled: bool = Field(False, alias="DEBUG_MODE")
    host: str = Field("0.0.0.0", alias="DEBUG_HOST")
    port: int = Field(5678, alias="DEBUG_PORT")
    wait_client: bool = Field(False, alias="WAIT_FOR_CLIENT")  # 设置为true的话 会阻塞线程直到连上


class AiProxyServerSettings(BaseAppSettings):
    admin_endpoint: str = Field(
        "https://ai-proxy.erda.cloud",
        alias="AI_PROXY_ADMIN_ENDPOINT",
        description="ai-proxy 管理接口端点",
    )
    admin_authorization: str = Field(
        alias="AI_PROXY_ADMIN_AUTHORIZATION",
        description="ai-proxy 管理接口授权",
    )
    ai_proxy_api_key: str = Field(alias="AI_PROXY_API_KEY")
    ai_proxy_base_url: str = Field("https://ai-proxy.erda.cloud/v1", alias="AI_PROXY_BASE")

    llm_timeout: int = Field(300, alias="LLM_TIMEOUT", description="llm 超时时间")

    ai_proxy_model_id_query_url: str = Field(
        "https://ai-proxy.erda.cloud/api/ai-proxy/clients/actions/get-by-access-key-id",
        alias="AI_PROXY_MODEL_ID_QUERY_URL",
    )

    ai_proxy_model_id_gpt_4_1: str = Field(
        alias="AI_PROXY_MODEL_ID_GPT_4_1",
        description="gpt-4.1 模型id",
    )

    ai_proxy_model_id_gpt_4o: str = Field(
        alias="AI_PROXY_MODEL_ID_GPT_4O",
        description="gpt-4o 模型id",
    )

    ai_proxy_model_id_gpt_4o_mini: str = Field(
        alias="AI_PROXY_MODEL_ID_GPT_4O_MINI",
        description="gpt-4o-mini 模型id",
    )


class LogLevel(str, Enum):
    TRACE = "TRACE"
    DEBUG = "DEBUG"
    INFO = "INFO"
    SUCCESS = "SUCCESS"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"


class LogSettings(BaseAppSettings):
    level: LogLevel = Field(default=LogLevel.INFO, description="日志级别", alias="LOG_LEVEL")
    backtrace: bool = Field(default=True, description="日志是否堆栈", alias="LOG_BACKTRACE")
    diagnose: bool = Field(
        default=True,
        description="日志打印堆栈的时候是否打印局部变量",
        alias="LOG_DIAGNOSE",
    )


class AgentWarmupSettings(BaseAppSettings):
    """Agent预热配置设置"""

    warmup_enabled: bool = Field(default=False, alias="AGENT_WARMUP_ENABLED", description="是否启用Agent预热")

    agent_metadata_domain: str = Field(
        default="http://localhost:8080",
        alias="AGENT_METADATA_DOMAIN",
        description="Agent元数据查询接口域名",
    )

    warmup_project_codes: str = Field(
        default="",
        alias="AGENT_WARMUP_PROJECT_CODES",
        description="需要预热的项目代码列表，用逗号分隔，为空表示预热所有项目",
    )


class RedisSettings(BaseAppSettings):
    """Redis配置"""
    host: str = Field(default="localhost", alias="REDIS_HOST", description="Redis服务器地址")
    port: int = Field(default=6379, alias="REDIS_PORT", description="Redis服务器端口")
    password: str = Field(default="", alias="REDIS_PASSWORD", description="Redis密码")
    db: int = Field(default=0, alias="REDIS_DB", description="Redis数据库编号")
    decode_responses: bool = Field(default=True, alias="REDIS_DECODE_RESPONSES", description="是否解码响应")
    socket_timeout: int = Field(default=5, alias="REDIS_SOCKET_TIMEOUT", description="Socket超时时间")
    socket_connect_timeout: int = Field(default=5, alias="REDIS_SOCKET_CONNECT_TIMEOUT", description="Socket连接超时时间")


class TracingSettings(BaseAppSettings):
    enabled: bool = Field(default=True, alias="TRACING_ENABLED")
    endpoint: str = Field(default="https://phoenix.daily.terminus.io/v1/traces", alias="TRACING_ENDPOINT")
    api_key: str = Field(
        default="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJqdGkiOiJBcGlLZXk6MiJ9.AO5DTOtc5OKjK4BtYzS8eYfnwn7X_QUWMIiLe7dC9m0",
        alias="TRACING_API_KEY",
    )


class AppSettings(BaseAppSettings):
    server: ServerSettings = ServerSettings()
    debug_settings: DebugSettings = DebugSettings()
    ai_proxy: AiProxyServerSettings = AiProxyServerSettings()
    log_settings: LogSettings = LogSettings()
    agent_warmup: AgentWarmupSettings = AgentWarmupSettings()
    tracing_settings: TracingSettings = TracingSettings()
    redis: RedisSettings = RedisSettings()
