import contextvars
import json
import uuid
from typing import Optional

from fastapi import Request
from loguru import logger
from pydantic import BaseModel, Field

from ..constants import TRACE_ID_HEADER_NAME


class ReqCtxHeader(BaseModel):
    accept_language: Optional[str] = Field(None, alias="ACCEPT-LANGUAGE")
    t_ai_source_cookie: Optional[str] = Field(None, alias="T-AI-SOURCE-COOKIE")
    t_ai_source_referer: Optional[str] = Field(None, alias="T-AI-SOURCE-REFERER")
    t_ai_source_authorization: Optional[str] = Field(None, alias="T-AI-SOURCE-AUTHORIZATION")
    t_ai_user_id: Optional[str] = Field(None, alias="T-AI-USER-ID")
    t_ai_user_name: Optional[str] = Field(None, alias="T-AI-USER-NAME")
    t_ai_user_encode: Optional[str] = Field(None, alias="T-AI-USER")
    t_ai_callback: Optional[str] = Field(None, alias="T-AI-CALLBACK")
    t_ai_source: Optional[str] = Field(None, alias="T-AI-SOURCE")
    t_ai_source_branch: Optional[str] = Field(None, alias="T-AI-SOURCE-BRANCH")
    t_ai_source_origin_org_id: Optional[str] = Field(None, alias="T-AI-SOURCE-ORIGIN-ORG-ID")
    t_ai_source_tenant_id: Optional[str] = Field(None, alias="T-AI-SOURCE-TENANT-ID")
    t_ai_source_time_zone: Optional[str] = Field(None, alias="T-AI-SOURCE-TIME-ZONE")
    t_ai_source_chain: Optional[str] = Field(None, alias="T-AI-SOURCE-CHAIN")
    t_ai_source_team: Optional[str] = Field(None, alias="T-AI-SOURCE-TEAM")
    t_ai_source_team_code: Optional[str] = Field(None, alias="T-AI-SOURCE-TEAM-CODE")
    t_ai_source_app: Optional[str] = Field(None, alias="T-AI-SOURCE-APP")
    t_ai_source_portal: Optional[str] = Field(None, alias="T-AI-SOURCE-PORTAL")


# 创建Pydantic模型来表示请求上下文
class ReqCtxModel(BaseModel):
    header: ReqCtxHeader
    trace_id: str = Field(description="trace id")


class ReqCtx:
    """请求上下文管理类 用于在异步环境获取相关信息"""

    _var = contextvars.ContextVar[Optional[ReqCtxModel]]("_request_ctx_", default=None)

    @classmethod
    def set_from_request(cls, request: Request) -> None:
        """从请求头设置上下文变量"""
        logger.debug(f"Setting request context from headers: {json.dumps(dict(request.headers))}")
        header_model = ReqCtxHeader(
            **{
                "ACCEPT-LANGUAGE": request.headers.get("Accept-Language"),
                "T-AI-SOURCE-COOKIE": request.headers.get("T-AI-SOURCE-COOKIE", request.headers.get("Cookie")),
                "T-AI-SOURCE-REFERER": request.headers.get("T-AI-SOURCE-REFERER", request.headers.get("Referer")),
                "T-AI-SOURCE-AUTHORIZATION": request.headers.get(
                    "T-AI-SOURCE-AUTHORIZATION", request.headers.get("Authorization")
                ),
                "T-AI-USER-ID": request.headers.get("T-AI-USER-ID"),
                "T-AI-USER-NAME": request.headers.get("T-AI-USER-NAME"),
                "T-AI-CALLBACK": request.headers.get("T-AI-CALLBACK"),
                "T-AI-SOURCE": request.headers.get("T-AI-SOURCE"),
                "T-AI-SOURCE-BRANCH": request.headers.get("T-AI-SOURCE-BRANCH"),
                "T-AI-SOURCE-ORIGIN-ORG-ID": request.headers.get("T-AI-SOURCE-ORIGIN-ORG-ID"),
                "T-AI-SOURCE-TENANT-ID": request.headers.get("T-AI-SOURCE-TENANT-ID"),
                "T-AI-SOURCE-TIME-ZONE": request.headers.get("T-AI-SOURCE-TIME-ZONE"),
                "T-AI-SOURCE-CHAIN": request.headers.get("T-AI-SOURCE-CHAIN"),
                "T-AI-SOURCE-TEAM": request.headers.get("T-AI-SOURCE-TEAM", request.headers.get("Trantor2-Team")),
                "T-AI-SOURCE-APP": request.headers.get("T-AI-SOURCE-APP"),
                "T-AI-USER": request.headers.get("T-AI-USER"),
                "T-AI-SOURCE-PORTAL": request.headers.get("T-AI-SOURCE-PORTAL"),
            }
        )
        # handle trace_id
        trace_id = request.headers.get(TRACE_ID_HEADER_NAME, str(uuid.uuid4()))
        req_ctx = ReqCtxModel(
            header=header_model,
            trace_id=trace_id,
        )
        cls._var.set(req_ctx)
        logger.debug(f"Request context set: {req_ctx}")

    @classmethod
    def get_context(cls) -> Optional[ReqCtxModel]:
        """获取当前上下文"""
        return cls._var.get()

    @classmethod
    def get_header(cls) -> Optional[ReqCtxHeader]:
        """获取当前请求头信息"""
        ctx = cls.get_context()
        return ctx.header if ctx else None

    @classmethod
    def get_trace_id(cls) -> str:
        """获取当前请求上下文的 trace_id"""
        ctx = cls.get_context()
        return ctx.trace_id if ctx else "None"

    @classmethod
    def get_lang(cls) -> Optional[str]:
        """获取当前请求上下文的 lang"""
        ctx = cls.get_context()
        return ctx.header.accept_language if ctx else None

    @classmethod
    def get_team_id(cls) -> Optional[str]:
        """获取当前请求上下文的 team_id"""
        ctx = cls.get_context()
        return ctx.header.t_ai_source_team if ctx else None

    @classmethod
    def get_team_code(cls) -> Optional[str]:
        """获取当前请求上下文的 team_code"""
        ctx = cls.get_context()
        return ctx.header.t_ai_source_team_code if ctx else None

    @classmethod
    def get_callback_url(cls) -> Optional[str]:
        """获取当前请求上下文的 callback_url"""
        ctx = cls.get_context()
        return ctx.header.t_ai_callback if ctx else None

    @classmethod
    def get_user_id(cls) -> Optional[str]:
        """获取当前请求上下文的 user_id"""
        ctx = cls.get_context()
        return ctx.header.t_ai_user_id if ctx else None

    @classmethod
    def reset(cls) -> None:
        """清空上下文变量"""
        cls._var.set(None)
