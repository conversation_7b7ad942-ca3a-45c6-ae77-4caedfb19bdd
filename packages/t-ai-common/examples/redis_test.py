"""
Redis客户端测试示例
"""
from t_ai_common.clients.redis_client import RedisClient, RedisSettings, get_redis_client


def test_redis_basic():
    """测试Redis基本功能"""
    print("=== Redis客户端基本功能测试 ===\n")
    
    # 1. 创建Redis客户端
    print("1. 创建Redis客户端...")
    redis_settings = RedisSettings()
    print(f"   Redis配置: {redis_settings.host}:{redis_settings.port}")
    
    redis_client = RedisClient(redis_settings)
    
    # 2. 测试连接
    print("\n2. 测试Redis连接...")
    try:
        if redis_client.ping():
            print("✅ Redis连接成功")
        else:
            print("❌ Redis连接失败")
            return
    except Exception as e:
        print(f"❌ Redis连接异常: {e}")
        return
    
    # 3. 测试基本操作
    print("\n3. 测试基本操作...")
    
    # 设置字符串值
    test_key = "test:redis:client"
    test_value = "Hello Redis!"
    
    if redis_client.set(test_key, test_value):
        print(f"✅ 设置值成功: {test_key} = {test_value}")
    else:
        print(f"❌ 设置值失败")
        return
    
    # 获取字符串值
    retrieved_value = redis_client.get(test_key)
    if retrieved_value == test_value:
        print(f"✅ 获取值成功: {test_key} = {retrieved_value}")
    else:
        print(f"❌ 获取值失败，期望: {test_value}, 实际: {retrieved_value}")
    
    # 4. 测试JSON操作
    print("\n4. 测试JSON操作...")
    
    json_key = "test:redis:json"
    json_value = {
        "name": "测试数据",
        "type": "JSON",
        "data": {
            "numbers": [1, 2, 3],
            "text": "Hello World"
        }
    }
    
    if redis_client.set_json(json_key, json_value):
        print(f"✅ 设置JSON值成功")
    else:
        print(f"❌ 设置JSON值失败")
        return
    
    retrieved_json = redis_client.get_json(json_key)
    if retrieved_json == json_value:
        print(f"✅ 获取JSON值成功")
        print(f"   数据: {retrieved_json}")
    else:
        print(f"❌ 获取JSON值失败")
        print(f"   期望: {json_value}")
        print(f"   实际: {retrieved_json}")
    
    # 5. 测试键存在性
    print("\n5. 测试键存在性...")
    
    if redis_client.exists(test_key):
        print(f"✅ 键存在检查成功: {test_key}")
    else:
        print(f"❌ 键存在检查失败: {test_key}")
    
    # 6. 清理测试数据
    print("\n6. 清理测试数据...")
    
    if redis_client.delete(test_key):
        print(f"✅ 删除键成功: {test_key}")
    else:
        print(f"❌ 删除键失败: {test_key}")
    
    if redis_client.delete(json_key):
        print(f"✅ 删除JSON键成功: {json_key}")
    else:
        print(f"❌ 删除JSON键失败: {json_key}")
    
    print(f"\n=== 测试完成 ===")


def test_redis_singleton():
    """测试Redis客户端单例模式"""
    print("\n=== Redis客户端单例模式测试 ===\n")
    
    # 获取两个客户端实例
    client1 = get_redis_client()
    client2 = get_redis_client()
    
    # 检查是否是同一个实例
    if client1 is client2:
        print("✅ 单例模式工作正常，两个客户端是同一个实例")
    else:
        print("❌ 单例模式失败，两个客户端不是同一个实例")
    
    # 测试连接
    try:
        if client1.ping():
            print("✅ 单例客户端连接正常")
        else:
            print("❌ 单例客户端连接失败")
    except Exception as e:
        print(f"❌ 单例客户端连接异常: {e}")


if __name__ == "__main__":
    test_redis_basic()
    test_redis_singleton()
