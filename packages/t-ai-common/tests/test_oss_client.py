"""
OSS客户端测试

测试OSS客户端的基本功能，包括上传、下载、删除等操作。
"""

import pytest
from unittest.mock import Mock, patch
from t_ai_common.clients.oss_client import OSSClient, OSSSettings, get_oss_client, init_oss_client


class TestOSSSettings:
    """测试OSS配置设置"""

    def test_oss_settings_defaults(self):
        """测试OSS设置的默认值"""
        settings = OSSSettings()
        assert settings.oss_access_key_id == ""
        assert settings.oss_access_key_secret == ""
        assert settings.bucket == ""
        assert settings.endpoint == ""
        assert settings.region == ""

    def test_oss_settings_from_env(self):
        """测试从环境变量加载OSS设置"""
        with patch.dict('os.environ', {
            'OSS_ACCESS_KEY_ID': 'test_key_id',
            'OSS_ACCESS_KEY_SECRET': 'test_key_secret',
            'OSS_BUCKET': 'test_bucket',
            'OSS_ENDPOINT': 'test.endpoint.com',
            'OSS_REGION': 'test-region'
        }):
            settings = OSSSettings()
            assert settings.oss_access_key_id == "test_key_id"
            assert settings.oss_access_key_secret == "test_key_secret"
            assert settings.bucket == "test_bucket"
            assert settings.endpoint == "test.endpoint.com"
            assert settings.region == "test-region"


class TestOSSClient:
    """测试OSS客户端"""

    @pytest.fixture
    def mock_settings(self):
        """创建模拟的OSS设置"""
        settings = OSSSettings()
        settings.oss_access_key_id = "test_key_id"
        settings.oss_access_key_secret = "test_key_secret"
        settings.bucket = "test_bucket"
        settings.endpoint = "test.endpoint.com"
        settings.region = "test-region"
        return settings

    @pytest.fixture
    def oss_client(self, mock_settings):
        """创建OSS客户端实例"""
        with patch('t_ai_common.clients.oss_client.oss.Client') as mock_client:
            client = OSSClient(mock_settings)
            client.client = mock_client
            return client

    def test_oss_client_initialization(self, mock_settings):
        """测试OSS客户端初始化"""
        with patch('t_ai_common.clients.oss_client.oss.Client') as mock_client:
            client = OSSClient(mock_settings)
            assert client.settings == mock_settings
            assert client.bucket_name == "test_bucket"
            mock_client.assert_called_once()

    def test_get_object_url(self, oss_client):
        """测试获取对象URL"""
        url = oss_client.get_object_url("test/key.png")
        expected_url = "https://test_bucket.test.endpoint.com/test/key.png"
        assert url == expected_url

    def test_generate_oss_key(self, oss_client):
        """测试生成OSS对象键名"""
        key = oss_client._generate_oss_key("test.png", "images")
        assert key.startswith("images/")
        assert key.endswith("_test.png")
        # 检查格式：images/YYYYMMDD_HHMMSS_uuid_filename
        parts = key.split("_")
        assert len(parts) >= 4  # prefix_timestamp_uuid_filename
        assert parts[0] == "images/20250811"  # 日期部分
        assert len(parts[1]) == 6  # 时间部分 HHMMSS
        assert len(parts[2]) == 8  # UUID部分
        assert parts[3] == "test.png"  # 文件名部分

    def test_get_key_from_url(self):
        """测试从URL提取对象键名"""
        url = "https://bucket.endpoint.com/path/to/file.png"
        key = OSSClient.get_key_from_url(url)
        assert key == "path/to/file.png"

    def test_get_filename_from_content_disposition(self):
        """测试从Content-Disposition提取文件名"""
        # 测试RFC 5987格式
        content_disposition = 'attachment; filename*=UTF-8\'\'test%20file.png'
        filename = OSSClient.get_filename_from_content_disposition(content_disposition)
        assert filename == "test file.png"

        # 测试常规格式
        content_disposition = 'attachment; filename="test_file.png"'
        filename = OSSClient.get_filename_from_content_disposition(content_disposition)
        assert filename == "test_file.png"

        # 测试无Content-Disposition
        filename = OSSClient.get_filename_from_content_disposition(None)
        assert filename is None

    def test_get_filename_from_key(self):
        """测试从对象键名提取文件名"""
        filename = OSSClient.get_filename_from_key("path/to/file.png")
        assert filename == "file.png"

        filename = OSSClient.get_filename_from_key("file.png")
        assert filename == "file.png"

        filename = OSSClient.get_filename_from_key(None)
        assert filename is None


class TestOSSClientFunctions:
    """测试OSS客户端函数"""

    def test_get_oss_client_singleton(self):
        """测试获取OSS客户端单例"""
        with patch('t_ai_common.clients.oss_client.OSSClient') as mock_client_class:
            # 第一次调用
            client1 = get_oss_client()
            mock_client_class.assert_called_once()

            # 第二次调用应该返回同一个实例
            client2 = get_oss_client()
            assert client1 == client2
            # 确保没有创建新的实例
            assert mock_client_class.call_count == 1

    def test_init_oss_client(self):
        """测试初始化OSS客户端"""
        settings = OSSSettings()
        settings.oss_access_key_id = "test_key_id"
        settings.oss_access_key_secret = "test_key_secret"
        settings.bucket = "test_bucket"
        settings.endpoint = "test.endpoint.com"
        settings.region = "test-region"

        with patch('t_ai_common.clients.oss_client.OSSClient') as mock_client_class:
            client = init_oss_client(settings)
            mock_client_class.assert_called_once_with(settings)
            assert client == mock_client_class.return_value


if __name__ == "__main__":
    pytest.main([__file__])