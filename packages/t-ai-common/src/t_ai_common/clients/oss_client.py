"""
OSS客户端封装

提供统一的OSS操作接口，支持文件上传、下载、管理等功能。
"""

import io
import re
import time
from pathlib import Path
from typing import Optional, Union, List
from urllib.parse import unquote, urlparse

import alibabacloud_oss_v2 as oss
from alibabacloud_oss_v2.types import BodyType
from loguru import logger
from pydantic import BaseModel, Field, ConfigDict
from pydantic_settings import BaseSettings
import uuid


class OSSSettings(BaseSettings):
    """OSS配置设置"""

    model_config = ConfigDict(
        env_file_encoding="utf-8",
        case_sensitive=False,
        extra="allow"
    )

    endpoint: str = Field(
        default="",
        alias="OSS_ENDPOINT",
        description="OSS endpoint"
    )
    oss_access_key_id: str = Field(
        default="",
        alias="OSS_ACCESS_KEY_ID",
        description="OSS access key id"
    )
    oss_access_key_secret: str = Field(
        default="",
        alias="OSS_ACCESS_KEY_SECRET",
        description="OSS access key secret"
    )
    bucket: str = Field(
        default="",
        alias="OSS_BUCKET",
        description="OSS bucket name"
    )
    region: str = Field(
        default="",
        alias="OSS_REGION",
        description="OSS region"
    )


class OSSClient:
    """OSS客户端封装类"""

    def __init__(self, settings: Optional[OSSSettings] = None):
        """
        初始化OSS客户端

        Args:
            settings: OSS配置设置，如果为None则从环境变量加载
        """
        self.settings = settings or OSSSettings()
        self.client = self._create_client()
        self.bucket_name = self.settings.bucket

    def _create_client(self) -> oss.Client:
        """
        创建OSS客户端

        Returns:
            oss.Client: OSS客户端实例
        """
        credentials_provider = oss.credentials.StaticCredentialsProvider(
            access_key_id=self.settings.oss_access_key_id,
            access_key_secret=self.settings.oss_access_key_secret,
        )
        cfg = oss.config.load_default()
        cfg.credentials_provider = credentials_provider
        cfg.region = self.settings.region
        cfg.endpoint = self.settings.endpoint

        return oss.Client(cfg)

    def put_object(
        self,
        key: str,
        data: Union[bytes, str, io.BytesIO],
        content_type: Optional[str] = None
    ) -> str:
        """
        上传对象到OSS

        Args:
            key: 对象键名
            data: 要上传的数据
            content_type: 内容类型

        Returns:
            str: 上传后的对象URL
        """
        try:
            # 处理不同类型的输入数据
            if isinstance(data, str):
                body = data.encode('utf-8')
            elif isinstance(data, io.BytesIO):
                body = data.getvalue()
            else:
                body = data

            request = oss.PutObjectRequest(
                bucket=self.bucket_name,
                key=key,
                body=body
            )

            if content_type:
                request.content_type = content_type

            self.client.put_object(request)

            # 返回对象URL
            return self.get_object_url(key)

        except Exception as e:
            logger.error(f"上传对象失败: {e}")
            raise

    def get_object(self, key: str) -> oss.GetObjectResult:
        """
        获取OSS对象

        Args:
            key: 对象键名

        Returns:
            oss.GetObjectResult: OSS对象结果
        """
        try:
            return self.client.get_object(
                oss.GetObjectRequest(
                    bucket=self.bucket_name,
                    key=key
                )
            )
        except Exception as e:
            logger.error(f"获取对象失败: {e}")
            raise

    def get_object_url(self, key: str) -> str:
        """
        获取对象URL

        Args:
            key: 对象键名

        Returns:
            str: 对象URL
        """
        return f"https://{self.bucket_name}.{self.settings.endpoint}/{key}"

    def _generate_oss_key(self, filename: str, prefix: str = "images") -> str:
        """
        生成OSS对象键名

        Args:
            filename: 文件名
            prefix: 前缀路径

        Returns:
            str: 生成的OSS对象键名
        """
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        unique_id = str(uuid.uuid4())[:8]
        return f"{prefix}/{timestamp}_{unique_id}_{filename}"

    def list_objects(self, prefix: Optional[str] = None, max_keys: int = 100) -> List[str]:
        """
        列出对象

        Args:
            prefix: 前缀过滤
            max_keys: 最大返回数量

        Returns:
            List[str]: 对象键名列表
        """
        try:
            request = oss.ListObjectsV2Request(
                bucket=self.bucket_name,
                prefix=prefix,
                max_keys=max_keys
            )
            result = self.client.list_objects_v2(request)
            return [obj.key for obj in result.contents] if result.contents else []
        except Exception as e:
            logger.error(f"列出对象失败: {e}")
            raise

    def delete_object(self, key: str) -> bool:
        """
        删除对象

        Args:
            key: 对象键名

        Returns:
            bool: 删除是否成功
        """
        try:
            self.client.delete_object(
                oss.DeleteObjectRequest(
                    bucket=self.bucket_name,
                    key=key
                )
            )
            return True
        except Exception as e:
            logger.error(f"删除对象失败: {e}")
            return False

    def object_exists(self, key: str) -> bool:
        """
        检查对象是否存在

        Args:
            key: 对象键名

        Returns:
            bool: 对象是否存在
        """
        try:
            self.get_object(key)
            return True
        except:
            return False

    @staticmethod
    def get_key_from_url(url: str) -> str:
        """
        从URL中提取对象键名

        Args:
            url: OSS对象URL

        Returns:
            str: 对象键名
        """
        parsed_url = urlparse(url)
        return parsed_url.path.lstrip("/")

    @staticmethod
    def get_filename_from_content_disposition(content_disposition: Optional[str]) -> Optional[str]:
        """
        从Content-Disposition头中提取文件名

        Args:
            content_disposition: Content-Disposition头值

        Returns:
            Optional[str]: 文件名
        """
        if not content_disposition:
            return None

        # 首先尝试解析 filename* 参数(RFC 5987 格式)
        filename_star_match = re.search(r"filename\*=UTF-8''([^;]*)", content_disposition)
        if filename_star_match:
            return unquote(filename_star_match.group(1))

        # 如果没有 filename*，尝试解析常规 filename 参数
        filename_match = re.search(r"filename=([^;]*)", content_disposition)
        if filename_match:
            filename = filename_match.group(1)
            # 移除可能的引号
            if filename.startswith('"') and filename.endswith('"'):
                filename = filename[1:-1]
            return unquote(filename)

        return None

    @staticmethod
    def get_filename_from_key(key: Optional[str]) -> Optional[str]:
        """
        从对象键名中提取文件名

        Args:
            key: 对象键名

        Returns:
            Optional[str]: 文件名
        """
        if not key:
            return None
        return key.split("/")[-1]


# 全局OSS客户端实例
_oss_client: Optional[OSSClient] = None


def get_oss_client() -> OSSClient:
    """
    获取全局OSS客户端实例

    Returns:
        OSSClient: OSS客户端实例
    """
    global _oss_client
    if _oss_client is None:
        _oss_client = OSSClient()
    return _oss_client


def init_oss_client(settings: OSSSettings) -> OSSClient:
    """
    初始化OSS客户端

    Args:
        settings: OSS配置设置

    Returns:
        OSSClient: 初始化的OSS客户端实例
    """
    global _oss_client
    _oss_client = OSSClient(settings)
    return _oss_client