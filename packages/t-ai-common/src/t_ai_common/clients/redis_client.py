import json
from typing import Any, Optional
from functools import lru_cache

import redis
from loguru import logger

from t_ai_app import G


class RedisClient:
    """Redis客户端封装类"""
    
    def __init__(self):
        self._client: Optional[redis.Redis] = None
    
    @property
    def client(self) -> redis.Redis:
        """获取Redis客户端实例（延迟初始化）"""
        if self._client is None:
            redis_settings = G.APP_SETTING.redis
            self._client = redis.Redis(
                host=redis_settings.host,
                port=redis_settings.port,
                password=redis_settings.password if redis_settings.password else None,
                db=redis_settings.db,
                decode_responses=redis_settings.decode_responses,
                socket_timeout=redis_settings.socket_timeout,
                socket_connect_timeout=redis_settings.socket_connect_timeout,
            )
            logger.info(f"Redis客户端已初始化: {redis_settings.host}:{redis_settings.port}")
        return self._client
    
    def get(self, key: str) -> Optional[str]:
        """获取Redis中的值"""
        try:
            return self.client.get(key)
        except Exception as e:
            logger.error(f"Redis GET操作失败，key: {key}, 错误: {e}")
            return None
    
    def set(self, key: str, value: str, ex: Optional[int] = None) -> bool:
        """设置Redis中的值"""
        try:
            return self.client.set(key, value, ex=ex)
        except Exception as e:
            logger.error(f"Redis SET操作失败，key: {key}, 错误: {e}")
            return False
    
    def get_json(self, key: str) -> Optional[dict]:
        """获取Redis中的JSON值"""
        try:
            value = self.get(key)
            if value is None:
                return None
            return json.loads(value)
        except json.JSONDecodeError as e:
            logger.error(f"Redis JSON解析失败，key: {key}, 错误: {e}")
            return None
        except Exception as e:
            logger.error(f"Redis GET JSON操作失败，key: {key}, 错误: {e}")
            return None
    
    def set_json(self, key: str, value: dict, ex: Optional[int] = None) -> bool:
        """设置Redis中的JSON值"""
        try:
            json_str = json.dumps(value, ensure_ascii=False)
            return self.set(key, json_str, ex=ex)
        except Exception as e:
            logger.error(f"Redis SET JSON操作失败，key: {key}, 错误: {e}")
            return False
    
    def delete(self, key: str) -> bool:
        """删除Redis中的键"""
        try:
            return bool(self.client.delete(key))
        except Exception as e:
            logger.error(f"Redis DELETE操作失败，key: {key}, 错误: {e}")
            return False
    
    def exists(self, key: str) -> bool:
        """检查Redis中的键是否存在"""
        try:
            return bool(self.client.exists(key))
        except Exception as e:
            logger.error(f"Redis EXISTS操作失败，key: {key}, 错误: {e}")
            return False
    
    def ping(self) -> bool:
        """测试Redis连接"""
        try:
            return self.client.ping()
        except Exception as e:
            logger.error(f"Redis PING操作失败，错误: {e}")
            return False


@lru_cache()
def get_redis_client() -> RedisClient:
    """获取Redis客户端实例（单例模式）"""
    return RedisClient()
