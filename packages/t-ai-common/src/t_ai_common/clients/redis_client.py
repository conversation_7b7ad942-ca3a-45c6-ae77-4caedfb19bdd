import json
from typing import Any, Optional
from functools import lru_cache

import redis
from loguru import logger
from pydantic import Field
from pydantic_settings import BaseSettings


class RedisSettings(BaseSettings):
    """Redis配置"""
    host: str = Field(default="localhost", alias="REDIS_HOST", description="Redis服务器地址")
    port: int = Field(default=6379, alias="REDIS_PORT", description="Redis服务器端口")
    password: str = Field(default="", alias="REDIS_PASSWORD", description="Redis密码")
    db: int = Field(default=0, alias="REDIS_DB", description="Redis数据库编号")
    decode_responses: bool = Field(default=True, alias="REDIS_DECODE_RESPONSES", description="是否解码响应")
    socket_timeout: int = Field(default=5, alias="REDIS_SOCKET_TIMEOUT", description="Socket超时时间")
    socket_connect_timeout: int = Field(default=5, alias="REDIS_SOCKET_CONNECT_TIMEOUT", description="Socket连接超时时间")

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False
        extra = "ignore"
        env_ignore_empty = True


class RedisClient:
    """Redis客户端封装类"""

    def __init__(self, redis_settings: Optional[RedisSettings] = None):
        self._client: Optional[redis.Redis] = None
        self._settings = redis_settings or RedisSettings()

    @property
    def client(self) -> redis.Redis:
        """获取Redis客户端实例（延迟初始化）"""
        if self._client is None:
            self._client = redis.Redis(
                host=self._settings.host,
                port=self._settings.port,
                password=self._settings.password if self._settings.password else None,
                db=self._settings.db,
                decode_responses=self._settings.decode_responses,
                socket_timeout=self._settings.socket_timeout,
                socket_connect_timeout=self._settings.socket_connect_timeout,
            )
            logger.info(f"Redis客户端已初始化: {self._settings.host}:{self._settings.port}")
        return self._client
    
    def get(self, key: str) -> Optional[str]:
        """获取Redis中的值"""
        try:
            return self.client.get(key)
        except Exception as e:
            logger.error(f"Redis GET操作失败，key: {key}, 错误: {e}")
            return None
    
    def set(self, key: str, value: str, ex: Optional[int] = None) -> bool:
        """设置Redis中的值"""
        try:
            return self.client.set(key, value, ex=ex)
        except Exception as e:
            logger.error(f"Redis SET操作失败，key: {key}, 错误: {e}")
            return False
    
    def get_json(self, key: str) -> Optional[dict]:
        """获取Redis中的JSON值"""
        try:
            value = self.get(key)
            if value is None:
                return None
            return json.loads(value)
        except json.JSONDecodeError as e:
            logger.error(f"Redis JSON解析失败，key: {key}, 错误: {e}")
            return None
        except Exception as e:
            logger.error(f"Redis GET JSON操作失败，key: {key}, 错误: {e}")
            return None
    
    def set_json(self, key: str, value: dict, ex: Optional[int] = None) -> bool:
        """设置Redis中的JSON值"""
        try:
            json_str = json.dumps(value, ensure_ascii=False)
            return self.set(key, json_str, ex=ex)
        except Exception as e:
            logger.error(f"Redis SET JSON操作失败，key: {key}, 错误: {e}")
            return False
    
    def delete(self, key: str) -> bool:
        """删除Redis中的键"""
        try:
            return bool(self.client.delete(key))
        except Exception as e:
            logger.error(f"Redis DELETE操作失败，key: {key}, 错误: {e}")
            return False
    
    def exists(self, key: str) -> bool:
        """检查Redis中的键是否存在"""
        try:
            return bool(self.client.exists(key))
        except Exception as e:
            logger.error(f"Redis EXISTS操作失败，key: {key}, 错误: {e}")
            return False
    
    def ping(self) -> bool:
        """测试Redis连接"""
        try:
            return self.client.ping()
        except Exception as e:
            logger.error(f"Redis PING操作失败，错误: {e}")
            return False


@lru_cache()
def get_redis_client(redis_settings: Optional[RedisSettings] = None) -> RedisClient:
    """获取Redis客户端实例（单例模式）"""
    return RedisClient(redis_settings)
