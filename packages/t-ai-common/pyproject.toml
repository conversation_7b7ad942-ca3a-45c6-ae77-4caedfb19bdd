[project]
name = "t-ai-common"
version = "0.1.0"
description = "t-ai-common 包 通用包"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
  "httpx>=0.28.1",
  # "httpx-sse>=0.4.0",
  "loguru>=0.7.3",
  "pydantic>=2.11.4",
  # "anyio>=4.9.0",
  "openai>=1.101.0",
  "mcp>=1.11.0",
  "orjson >= 3.10,<4",
  "alibabacloud-oss-v2",
  "pydantic-settings"
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"


[tool.hatch.build.targets.wheel]
packages = ["src/t_ai_common"]
