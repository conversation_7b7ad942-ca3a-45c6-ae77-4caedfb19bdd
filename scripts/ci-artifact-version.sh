#!/bin/bash

# 制品版本
function artifact_version() {
  version="t-ai2" #
  current_branch=$(git branch --show-current)
  # 主干分支(develop)
  [[ $current_branch == develop ]] && echo "$version-alpha" && return
  # 发布分支(release/*)
  [[ $current_branch == release/* ]] && echo "$version-$(echo "$current_branch" | cut -d "/" -f 2)"  && return
  # 其他分支
  echo "preview"
}
CURRENT_DATE=$(date "+%y%m%d.%H%M%S")
echo "action meta: artifact-version=$(artifact_version)+$CURRENT_DATE"
echo "action meta: image-tag=$(artifact_version).$CURRENT_DATE"
echo "action meta: current-date=$CURRENT_DATE"