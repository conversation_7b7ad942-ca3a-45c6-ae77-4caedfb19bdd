# T-AI2 项目 Cursor 规则

## 依赖管理规则
- 始终使用 `uv` 管理 Python 依赖，禁止使用 `pip` 或 `requirements.txt`
- 添加新依赖时，直接修改 `pyproject.toml` 文件，然后运行 `uv sync`
- 使用 `uv add package-name` 添加依赖，使用 `uv remove package-name` 移除依赖
- 保持 `uv.lock` 文件的完整性，提交时必须包含此文件
- 使用 `uv sync --frozen --no-cache -v` 进行精确的依赖同步

## 技术栈规则
- 使用 FastAPI 作为 Web 框架，所有路由函数必须是异步函数（async def）
- 使用 Pydantic 进行数据验证和序列化，定义所有数据模型
- 配置管理使用 pydantic-settings，继承 `BaseSettings` 类
- 日志记录使用 loguru，导入方式：`from loguru import logger`
- 测试使用 pytest，测试文件命名为 `test_*.py` 或 `*_test.py`

## 代码风格规则
- 所有函数必须有类型注解，包括参数和返回值
- 使用 Python 3.12+ 的新语法特性（如 type union: `str | None`）
- 优先使用异步编程模式，API 路由和数据库操作都应该是异步的
- 遵循 PEP 8 代码风格，使用 4 个空格缩进
- 使用中文注释和文档字符串，但变量名和函数名使用英文

## 项目结构规则
- 遵循项目的 workspace 结构，packages 下的每个子项目都是独立模块
- 导入本地模块时使用绝对导入路径
- 新建模块时要在对应的 `pyproject.toml` 中声明依赖关系
- 配置文件统一放在 `src/t_ai2/configuration/` 目录下

## FastAPI 特定规则
- 路由定义使用 APIRouter，按功能模块组织
- 请求/响应模型必须使用 Pydantic BaseModel
- 异常处理使用 FastAPI 的 HTTPException
- 依赖注入优先使用 FastAPI 的 Depends 系统
- API 文档字符串使用中文，包含详细的参数说明

## 调试和测试规则
- 调试使用 debugpy，通过环境变量 `DEBUG_MODE=true` 启用
- 测试文件必须包含对应的类型注解和异步测试
- 使用 pytest.mark.asyncio 装饰异步测试函数
- Mock 外部依赖时使用 pytest-asyncio 兼容的 mock 方法

## 错误处理规则
- 使用 loguru 记录错误，包含详细的上下文信息
- API 错误返回要包含中文错误信息和错误码
- 捕获异常时要记录完整的 traceback
- 关键操作失败时要有优雅降级机制

## 性能规则
- 数据库操作使用连接池和异步 ORM
- 大量数据处理使用异步生成器或流式处理
- 缓存使用 Redis 或内存缓存，避免重复计算
- API 响应使用适当的分页机制

## 安全规则
- 敏感信息通过环境变量配置，不要硬编码
- API 接口要有适当的权限验证
- 用户输入必须通过 Pydantic 验证
- 记录关键操作的审计日志

## 示例代码模板
当创建新的 FastAPI 路由时，使用以下模板：

```python
from fastapi import APIRouter, Depends, HTTPException
from pydantic import BaseModel
from loguru import logger
from typing import List

router = APIRouter(prefix="/api/v1", tags=["模块名"])

class RequestModel(BaseModel):
    """请求模型"""
    field: str

class ResponseModel(BaseModel):
    """响应模型"""
    result: str

@router.post("/endpoint", response_model=ResponseModel)
async def create_something(
    request: RequestModel,
    # dependencies: SomeDependency = Depends(get_dependency)
) -> ResponseModel:
    """
    创建某个资源

    Args:
        request: 请求参数

    Returns:
        ResponseModel: 创建结果

    Raises:
        HTTPException: 业务异常
    """
    try:
        logger.info(f"开始处理请求: {request}")
        # 业务逻辑
        result = await some_async_operation(request.field)
        return ResponseModel(result=result)
    except Exception as e:
        logger.error(f"处理失败: {e}")
        raise HTTPException(status_code=500, detail="内部服务器错误")
```

## 命令行工具规则
- 使用项目根目录的 Makefile 执行常见任务
- 虚拟环境激活：`source .venv/bin/activate`
- 依赖同步：`uv sync --frozen --no-cache -v`
- 运行测试：`uv run pytest`
- 启动服务：`uv run python -m uvicorn main:app --reload`

遵循这些规则将确保代码质量、项目一致性和开发效率。
