# ========================================
# T-AI2 项目环境变量配置示例
# ========================================
# 复制此文件为 .env 并根据实际情况修改配置值

# ========================================
# 服务器配置
# ========================================
# 服务器主机地址
SERVER_HOST=0.0.0.0
# 服务器端口
SERVER_PORT=8000
# 是否启用热重载
SERVER_RELOAD=false

# ========================================
# 调试配置
# ========================================
# 是否启用调试模式
DEBUG_MODE=true
# 调试主机地址
DEBUG_HOST=0.0.0.0
# 调试端口
DEBUG_PORT=5678
# 是否等待调试客户端连接
WAIT_FOR_CLIENT=false

# ========================================
# AI Proxy 配置
# ========================================
# AI Proxy 管理接口端点
AI_PROXY_ADMIN_ENDPOINT=https://ai-proxy.erda.cloud
# AI Proxy 管理接口授权
AI_PROXY_ADMIN_AUTHORIZATION=your_admin_authorization_here
# AI Proxy API Key
AI_PROXY_API_KEY=your_ai_proxy_api_key_here
# AI Proxy 基础 URL
AI_PROXY_BASE=https://ai-proxy.erda.cloud/v1
# LLM 超时时间（秒）
LLM_TIMEOUT=300
# AI Proxy 模型ID查询URL
AI_PROXY_MODEL_ID_QUERY_URL=https://ai-proxy.erda.cloud/api/ai-proxy/clients/actions/get-by-access-key-id
# GPT-4.1 模型ID
AI_PROXY_MODEL_ID_GPT_4_1=your_gpt_4_1_model_id_here
# GPT-4O 模型ID
AI_PROXY_MODEL_ID_GPT_4O=your_gpt_4o_model_id_here
# GPT-4O-Mini 模型ID
AI_PROXY_MODEL_ID_GPT_4O_MINI=your_gpt_4o_mini_model_id_here

# ========================================
# 日志配置
# ========================================
# 日志级别 (TRACE, DEBUG, INFO, SUCCESS, WARNING, ERROR, CRITICAL)
LOG_LEVEL=DEBUG
# 是否显示日志堆栈
LOG_BACKTRACE=true
# 是否显示局部变量
LOG_DIAGNOSE=true

# ========================================
# Agent 预热配置
# ========================================
# 是否启用Agent预热
AGENT_WARMUP_ENABLED=false
# Agent元数据查询接口域名
AGENT_METADATA_DOMAIN=http://localhost:8080
# 需要预热的项目代码列表，用逗号分隔，为空表示预热所有项目
AGENT_WARMUP_PROJECT_CODES=

# ========================================
# 向量化配置
# ========================================
# 嵌入模型名称
EMBEDDING_MODEL_NAME=text-embedding-ada-002
# 默认分块大小
DEFAULT_CHUNK_SIZE=800
# 默认分块重叠
DEFAULT_CHUNK_OVERLAP=200


# ========================================
# MySQL 数据库配置
# ========================================
# MySQL 用户名
MYSQL_USERNAME=your_mysql_username
# MySQL 密码
MYSQL_PASSWORD=your_mysql_password
# MySQL 主机地址
MYSQL_HOST=localhost
# MySQL 端口
MYSQL_PORT=3306
# MySQL 数据库名称
MYSQL_DATABASE_TCS=trantor_dev_agent_ops

# ========================================
# Langfuse 配置
# ========================================
# Langfuse 公钥
LANGFUSE_PUBLIC_KEY=your_langfuse_public_key
# Langfuse 密钥
LANGFUSE_SECRET_KEY=your_langfuse_secret_key
# Langfuse 主机地址
LANGFUSE_HOST=https://cloud.langfuse.com

# ========================================
# OSS 对象存储配置
# ========================================
# OSS Access Key ID
OSS_ACCESS_KEY_ID=your_oss_access_key_id
# OSS Access Key Secret
OSS_ACCESS_KEY_SECRET=your_oss_access_key_secret
# OSS Bucket 名称
OSS_BUCKET=your_oss_bucket_name
# OSS Endpoint
OSS_ENDPOINT=oss-cn-hangzhou.aliyuncs.com
# OSS Region
OSS_REGION=cn-hangzhou

# ========================================
# Azure OpenAI 配置
# ========================================
# Azure OpenAI 资源名称
AZURE_OPENAI_ENDPOINT=https://admin-mabyszq9-uaenorth.cognitiveservices.azure.com
AZURE_OPENAI_DEPLOYMENT_NAME=gpt-image-1
# Azure OpenAI API 密钥
AZURE_OPENAI_API_KEY=your_azure_openai_api_key
# Azure OpenAI API 版本
AZURE_OPENAI_API_VERSION=2025-04-01-preview
