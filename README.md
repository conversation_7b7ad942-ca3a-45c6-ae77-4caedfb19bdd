# 开发前准备
1. 安装依赖管理器
安装 [uv](https://docs.astral.sh/uv/) 用于依赖版本管理
代码clone下来后
可以直接使用 uv sync --frozen --no-cache -v 同步pyproject.toml中的版本依赖

2. 安装虚拟环境
推荐使用 uv venv --python 3.12 在本地创建虚拟环境进行开发
*虚拟环境 通过 source .venv/bin/activate 激活  deactivate 退出

3. 环境变量配置
项目使用 `.env` 文件管理环境变量，首次运行前需要配置：

```bash
# 从示例文件创建 .env 文件
make docker-env-setup

# 编辑 .env 文件，根据实际情况修改配置值
vim .env
```

**重要配置项说明：**
- `AI_PROXY_API_KEY`: AI Proxy 的 API 密钥（必需）
- `AI_PROXY_ADMIN_AUTHORIZATION`: AI Proxy 管理接口授权（必需）
- `MYSQL_USERNAME` / `MYSQL_PASSWORD`: MySQL 数据库连接信息
- `DEBUG_MODE`: 调试模式开关，开发时建议设为 `true`
- `LOG_LEVEL`: 日志级别，开发时建议设为 `DEBUG`

**图片生成和编辑API配置（新增）：**
- `OSS_ACCESS_KEY_ID`: 阿里云OSS Access Key ID（必需）
- `OSS_ACCESS_KEY_SECRET`: 阿里云OSS Access Key Secret（必需）
- `OSS_BUCKET`: OSS存储桶名称（必需）
- `OSS_ENDPOINT`: OSS服务端点，如 `oss-cn-hangzhou.aliyuncs.com`（必需）
- `OSS_REGION`: OSS地域，如 `cn-hangzhou`（必需）
- `AZURE_OPENAI_ENDPOINT`: Azure OpenAI服务端点（必需）
- `AZURE_OPENAI_DEPLOYMENT_NAME`: Azure OpenAI模型部署名称，如 `gpt-image-1`（必需）
- `AZURE_OPENAI_API_KEY`: Azure OpenAI API密钥（必需）
- `AZURE_OPENAI_API_VERSION`: Azure OpenAI API版本，如 `2025-04-01-preview`（必需）

详细的环境变量说明请参考 `env.example` 文件。

## 4. 添加代码规范机制

规范限制基于 git precommit hook 机制，基于：https://pre-commit.com/

### 4.1 安装 pre-commit

`pip install pre-commit`

### 4.2 安装 git hook scripts

`pre-commit install`

# 本地运行

## 方式一：直接运行（推荐开发使用）
```bash
# 激活虚拟环境
source .venv/bin/activate

# 开发模式（热重载 + 调试）
make dev

# 或仅调试模式
make debug_dev

# 或仅热重载模式
make reload_dev

# 或生产模式
make run
```

## 方式二：Docker 运行（推荐生产使用）

### 构建镜像
```bash
# 构建 Docker 镜像
make docker-build
```

### 运行容器
```bash
# 运行 Docker 容器（会自动加载 .env 文件中的环境变量）
make docker-run
```

### 管理容器
```bash
# 停止容器
make docker-stop

# 清理镜像和容器
make docker-clean
```

### 访问应用
- 应用地址：http://localhost:8000
- 调试端口：5678（需要 DEBUG_MODE=true）
- API 文档：http://localhost:8000/docs

## 图片生成和编辑API

项目新增了基于Azure OpenAI GPT-image-1 API的图片生成和编辑功能，生成的图片自动存储到阿里云OSS中。

### 功能特性

- **图片生成**：支持DALL-E 3和GPT-image-1模型，根据文本提示生成高质量图片
- **图片编辑**：支持GPT-image-1模型，对已有图片进行智能编辑
- **OSS存储**：所有生成的图片自动上传到阿里云OSS，提供稳定的存储和访问
- **RESTful API**：提供标准的REST API接口，支持JSON格式的请求和响应

# 技术选型
## web框架
fastapi + [uvicorn](https://www.uvicorn.org/)(ASGI) 全异步

## 类型系统
pydantic

## settings
使用pydantic-settings 做settings 可以轻松做到settings依赖注入
参考: https://fastapi.tiangolo.com/zh/advanced/settings/

## 依赖管理
uv

## 日志框架
logru

## 测试
pytest
参考: https://fastapi.tiangolo.com/zh/tutorial/testing/

### vscode debug test 当前文件脚本
```json
 {
            "name": "Python: Test current file",
            "type": "debugpy",
            "request": "launch",
            "module": "pytest",
            "console": "integratedTerminal",
            "cwd": "${workspaceFolder}",
            "python": "${workspaceFolder}/.venv/bin/python",
            "env": {
                "PYTHONPATH": "${workspaceFolder}"
            },
            "args": ["-v", "-s", "${file}"]
        }
```

## 调试
## [debugpy](https://github.com/microsoft/debugpy)
vscode 推荐 可以本地或者远程 具体如何使用见下
## [pydev](https://www.pydev.org/)
pycharm 内置集成 可以本地或者远程 具体如何使用见下

# 工具
根目录的makefile 提供了一些常见的工具。
注意要在python虚拟环境下运行。

# 调试方式
## 使用debugpy方式
使用debugpy 需要通过ENV DEBUG_MODE=true 开启
默认端口是 5678

### 工作原理
debugpy 会在程序侧启动一个子进程监听
需要调试器(IDE) 提供一个对接程序(DAP client)

关键点就是listen方法
注意这个方法同时只能有一个活跃连接

### vscode launch脚本对接程序
```json
{
    "version": "0.2.0",
    "configurations": [
        {
            "name": "FastAPI 本地调试",
            "type": "debugpy",
            "request": "attach",
            "connect": {
                "host": "127.0.0.1",
                "port": 5678
            }
        },
        {
            "name": "FastAPI 远程调试",
            "type": "debugpy",
            "request": "attach",
            "connect": {
                "host": "************",
                "port": 5678
            },
            "justMyCode": false,
            // refer: https://github.com/microsoft/debugpy/issues/1858
            "pathMappings": [
                {
                    "localRoot": "${workspaceFolder}",
                    "remoteRoot": "."
                }
            ]
        }
    ]
}
```

## 使用pydev
本地 直接用python 模块启动即可
远程 需要使用pyenv remote server
参考:
https://www.jetbrains.com/help/pycharm/remote-debugging-with-product.html
